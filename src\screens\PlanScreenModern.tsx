import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  Dimensions,
  SafeAreaView,
  Modal,
  TextInput,
  ImageBackground,
  StatusBar,
  Platform,
} from 'react-native';

import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
// Removed BlurView import
import Animated, {
  FadeInUp,
  SlideInLeft,
  FadeInDown,
  SlideInUp,
  SlideInRight,
  ZoomIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows, BlurIntensity } from '../constants/Colors';
import { AnimationConfig } from '../utils/AnimationUtils';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import LottieIcon from '../components/LottieIcon';
import { CircularProgress } from '../components/CircularProgress';
// Removed BlurView import
import * as Haptics from 'expo-haptics';
import ApiService from '../services/ApiService';
import { useProfile } from '../contexts/ProfileContext';
import DatabaseIntegrationService from '../services/DatabaseIntegrationService';
import NotificationService from '../services/NotificationService';
import ErrorHandlingService from '../services/ErrorHandlingService';
import FavoritesService from '../services/FavoritesService';
import WeeklyPlanManager, { WeekPlan as EnhancedWeekPlan, MealData } from '../services/WeeklyPlanManager';
import { WeightGoalTracker } from '../services/WeightGoalTracker';
import RecipeCacheService from '../services/RecipeCacheService';

const { width, height } = Dimensions.get('window');

// Types
interface MealPlan {
  day: string;
  meals: { [key: string]: { name: string; calories: number; time: string } };
  completed: boolean;
}

// Use the enhanced WeekPlan from WeeklyPlanManager
type WeekPlan = EnhancedWeekPlan;

const PlanScreenModern: React.FC = () => {
  const navigation = useNavigation();

  // Add error boundary for profile context
  let profile, addRecentMeal, notifyWeightProgressUpdate;
  try {
    const profileContext = useProfile();
    profile = profileContext.profile;
    addRecentMeal = profileContext.addRecentMeal;
    notifyWeightProgressUpdate = profileContext.notifyWeightProgressUpdate;
  } catch (error) {
    console.error('❌ Profile context error:', error);
    // Return early with error state
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fcf4ec' }}>
        <Text style={{ fontSize: 18, color: '#ff0000', textAlign: 'center', margin: 20 }}>
          Profile context error. Please restart the app.
        </Text>
      </View>
    );
  }

  // Add null checks for profile
  if (!profile) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fcf4ec' }}>
        <ActivityIndicator size="large" color="#6B7C5A" />
        <Text style={{ marginTop: 10, fontSize: 16, color: '#666' }}>Loading profile...</Text>
      </View>
    );
  }

  const dbService = DatabaseIntegrationService;
  const notificationService = NotificationService;

  // Core states - optimized for performance
  const [weekPlan, setWeekPlan] = useState<WeekPlan | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingType, setLoadingType] = useState<string | null>(null);
  const [selectedDay, setSelectedDay] = useState<string | null>(null);
  const [showCustomization, setShowCustomization] = useState(false);
  const [viewMode, setViewMode] = useState<'week' | 'day' | 'timeline'>('week');
  const [viewingRecipe, setViewingRecipe] = useState<string | null>(null);
  const [isPlanExpired, setIsPlanExpired] = useState(false);
  const [isUpdatingPlan, setIsUpdatingPlan] = useState(false); // Prevent multiple simultaneous updates

  // PERFORMANCE: Use ref to track update operations and prevent race conditions
  const updateOperationRef = useRef<Promise<any> | null>(null);

  // Voice recognition states
  const [isListening, setIsListening] = useState(false);
  const [voiceText, setVoiceText] = useState('');

  // Customization states - Initialize with user profile data
  const [customPrompt, setCustomPrompt] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [calorieTarget, setCalorieTarget] = useState(profile.caloriesGoal || 2000);
  const [proteinTarget, setProteinTarget] = useState(profile.proteinGoal || 150);
  const [mealCount, setMealCount] = useState(3);
  const [cookingTime, setCookingTime] = useState('30');
  const [budgetLevel, setBudgetLevel] = useState('medium');
  const [cuisinePreferences, setCuisinePreferences] = useState<string[]>(profile.preferredCuisines || []);
  const [allergens, setAllergens] = useState<string[]>(profile.allergies || []);
  const [dietaryStyle, setDietaryStyle] = useState('balanced');
  const [activityLevel, setActivityLevel] = useState(profile.activityLevel || 'moderate');

  // Update customization states when specific profile fields change (prevent infinite loops)
  useEffect(() => {
    if (profile) {
      setCalorieTarget(profile.caloriesGoal || 2000);
      setProteinTarget(profile.proteinGoal || 150);
      setCuisinePreferences(profile.preferredCuisines || []);
      setAllergens(profile.allergies || []);
      setActivityLevel(profile.activityLevel || 'moderate');

      // Also update dietary tags based on profile preferences
      if (profile.dietaryPreferences && profile.dietaryPreferences.length > 0) {
        setSelectedTags(profile.dietaryPreferences);
      }

      console.log('📊 Week plan updated with profile data:', {
        calories: profile.caloriesGoal,
        protein: profile.proteinGoal,
        cuisines: profile.preferredCuisines,
        allergies: profile.allergies,
        activity: profile.activityLevel,
        dietary: profile.dietaryPreferences
      });
    }
  }, [
    profile.caloriesGoal,
    profile.proteinGoal,
    profile.preferredCuisines,
    profile.allergies,
    profile.activityLevel,
    profile.dietaryPreferences,
    profile.isProfileComplete
  ]); // Use specific fields instead of entire profile object

  // Helper function to get meal info based on meal type
  const getMealInfo = (mealType: string, index: number) => {
    // Get user's preferred meal times from profile with enhanced debugging
    const userMealTimes = profile.preferredMealTimes || {
      breakfast: '08:00',
      lunch: '13:00',
      dinner: '19:00',
      snack: '15:00'
    };

    // Enhanced debug logging to track meal time issues
    console.log('🕐 MEAL TIME DEBUG:', {
      mealType,
      hasPreferredMealTimes: !!profile.preferredMealTimes,
      preferredMealTimes: profile.preferredMealTimes,
      userMealTimes,
      profileName: profile.name,
      isProfileComplete: profile.isProfileComplete
    });

    // Convert 24-hour format to 12-hour format for display
    const formatTime = (time24: string) => {
      console.log(`🕐 formatTime called with: "${time24}"`);

      if (!time24 || typeof time24 !== 'string' || !time24.includes(':')) {
        console.warn(`⚠️ Invalid time format: ${time24}, using fallback`);
        return '12:00 PM';
      }

      try {
        const [hours, minutes] = time24.split(':');
        const hour = parseInt(hours, 10);
        const min = minutes || '00';

        if (isNaN(hour) || hour < 0 || hour > 23) {
          console.warn(`⚠️ Invalid hour: ${hour}, using fallback`);
          return '12:00 PM';
        }

        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
        const result = `${displayHour}:${min} ${ampm}`;
        console.log(`🕐 formatTime result: "${result}"`);
        return result;
      } catch (error) {
        console.error(`❌ Error formatting time ${time24}:`, error);
        return '12:00 PM';
      }
    };

    const mealInfoMap: { [key: string]: { type: string; icon: string; time: string; calories: number } } = {
      breakfast: { type: 'Breakfast', icon: 'sunny', time: formatTime(userMealTimes.breakfast), calories: 350 },
      snack1: { type: 'Morning Snack', icon: 'nutrition', time: '10:00 AM', calories: 150 },
      lunch: { type: 'Lunch', icon: 'restaurant', time: formatTime(userMealTimes.lunch), calories: 450 },
      snack2: { type: 'Afternoon Snack', icon: 'apple', time: formatTime(userMealTimes.snack), calories: 150 },
      dinner: { type: 'Dinner', icon: 'moon', time: formatTime(userMealTimes.dinner), calories: 500 },
      snack3: { type: 'Evening Snack', icon: 'leaf', time: '9:00 PM', calories: 100 },
    };

    const result = mealInfoMap[mealType];
    if (!result) {
      console.warn(`⚠️ Unknown meal type: ${mealType}, using fallback`);
      return { type: 'Meal', icon: 'restaurant', time: '12:00 PM', calories: 300 };
    }

    // Debug log the final result
    console.log(`🕐 Meal time for ${mealType}: ${result.time} (from ${userMealTimes[mealType as keyof typeof userMealTimes] || 'fallback'})`);

    return result;
  };
  const [healthGoals, setHealthGoals] = useState<string[]>([]);

  // Enhanced weekly plan loading with profile timeout handling
  useEffect(() => {
    const loadWeeklyPlan = async () => {
      try {
        console.log('📅 Starting enhanced weekly plan loading...');

        // Set loading state at the beginning to prevent flashing
        setLoading(true);

        // Enhanced profile validation with fallback strategies
        if (!profile) {
          console.log('⚠️ No profile available, attempting plan load with fallback...');

          // Try to load existing plan without profile dependency
          try {
            const existingPlan = await WeeklyPlanManager.getActiveWeeklyPlan();
            if (existingPlan) {
              setWeekPlan(existingPlan);
              console.log('✅ Loaded existing plan without profile');
              return; // Don't set loading false here, let finally block handle it
            } else {
              // If no existing plan and no profile, try to initialize the system anyway
              console.log('🔄 No existing plan found, attempting system initialization...');
              await WeeklyPlanManager.initializeWeeklyPlanSystem(null);
            }
          } catch (fallbackError) {
            console.warn('⚠️ Fallback plan loading failed:', fallbackError);
          }

          setWeekPlan(null);
          return; // Don't set loading false here, let finally block handle it
        }

        // More lenient profile validation
        const hasBasicData = !!(profile.name || profile.age || profile.weight || profile.height);
        const hasPreferences = !!(profile.preferredMealTimes && Object.keys(profile.preferredMealTimes).length > 0);

        if (!profile.isProfileComplete && !hasBasicData) {
          console.log('⚠️ Profile has no usable data, attempting plan load with defaults...');

          // Try to load existing plan or create with minimal profile
          try {
            const existingPlan = await WeeklyPlanManager.getActiveWeeklyPlan();
            if (existingPlan) {
              setWeekPlan(existingPlan);
              console.log('✅ Loaded existing plan with incomplete profile');
              return; // Don't set loading false here, let finally block handle it
            }

            // Create minimal profile for plan generation
            const minimalProfile = {
              ...profile,
              name: profile.name || 'User',
              age: profile.age || 30,
              weight: profile.weight || 70,
              height: profile.height || 170,
              isProfileComplete: true,
              preferredMealTimes: profile.preferredMealTimes || {
                breakfast: '08:00',
                lunch: '12:00',
                dinner: '18:00'
              }
            };

            console.log('🔄 Attempting plan generation with minimal profile...');
            const planWithMinimal = await WeeklyPlanManager.initializeWeeklyPlanSystem(minimalProfile);
            if (planWithMinimal) {
              setWeekPlan(planWithMinimal);
              console.log('✅ Generated plan with minimal profile');
              return; // Don't set loading false here, let finally block handle it
            }
          } catch (minimalError) {
            console.warn('⚠️ Minimal profile plan generation failed:', minimalError);
          }

          setWeekPlan(null);
          return; // Don't set loading false here, let finally block handle it
        }

        console.log('📅 Initializing weekly plan system with profile...');
        console.log('🔍 Profile data at plan load:', {
          name: profile.name,
          isProfileComplete: profile.isProfileComplete,
          preferredMealTimes: profile.preferredMealTimes,
          hasRequiredData: hasBasicData,
          hasPreferences: hasPreferences
        });

        // FIXED: Only load existing plans, don't auto-generate new ones
        let currentPlan = null;

        // Strategy 1: Try to get existing active plan (READ-ONLY)
        try {
          console.log('🔄 Loading existing meal plan from storage...');
          currentPlan = await WeeklyPlanManager.getActiveWeeklyPlan();

          if (currentPlan) {
            console.log('✅ Found existing meal plan in storage');

            // Check if the plan is for the current week
            const currentDate = new Date();
            const currentWeekNumber = Math.ceil(((currentDate.getTime() - new Date(currentDate.getFullYear(), 0, 1).getTime()) / 86400000 + 1) / 7);
            const isCurrentWeekPlan = currentPlan.weekNumber === currentWeekNumber &&
                                     currentPlan.year === currentDate.getFullYear();

            if (!isCurrentWeekPlan) {
              console.log('⚠️ Existing plan is for a different week, but keeping it visible');
              console.log(`📅 Plan week: ${currentPlan.weekNumber}/${currentPlan.year}, Current week: ${currentWeekNumber}/${currentDate.getFullYear()}`);
              // Note: Don't auto-generate, let user decide to create new plan
            }
          } else {
            console.log('📅 No existing meal plan found - user needs to create one');
          }
        } catch (loadError) {
          console.warn('⚠️ Failed to load existing plan:', loadError);
          currentPlan = null;
        }

        if (currentPlan) {
          setWeekPlan(currentPlan);
          console.log('✅ Weekly plan system initialized with current week plan');
        } else {
          console.error('❌ Failed to initialize weekly plan system after all attempts');
          setWeekPlan(null);

          // Try to load any existing plan as fallback
          try {
            const fallbackPlan = await WeeklyPlanManager.getActiveWeeklyPlan();
            if (fallbackPlan) {
              console.log('🔄 Using fallback plan from storage');
              setWeekPlan(fallbackPlan);
            } else {
              // Last resort: emergency recovery
              console.log('🚨 Attempting emergency plan recovery...');
              const emergencyPlan = await WeeklyPlanManager.emergencyPlanRecovery(profile);
              if (emergencyPlan) {
                console.log('✅ Emergency recovery successful');
                setWeekPlan(emergencyPlan);
              } else {
                console.error('❌ All recovery attempts failed');
              }
            }
          } catch (fallbackError) {
            console.error('❌ Fallback plan loading failed:', fallbackError);
          }
        }
      } catch (error) {
        console.error('❌ Critical error in weekly plan management:', error);
        setWeekPlan(null);
      } finally {
        setLoading(false);
      }
    };

    // Add timeout to prevent hanging
    const timeoutId = setTimeout(() => {
      console.warn('⚠️ Weekly plan loading timed out');
      setLoading(false);
    }, 30000); // Increased timeout for better reliability

    loadWeeklyPlan().finally(() => {
      clearTimeout(timeoutId);
    });
  }, [profile]);

  // FIXED: Check for week expiration but don't auto-generate new plans
  useEffect(() => {
    const checkWeekExpiration = async () => {
      if (!profile || !weekPlan) {
        setIsPlanExpired(false);
        return;
      }

      const isValid = await WeeklyPlanManager.isCurrentPlanValid();
      if (!isValid) {
        console.log('📅 Current plan has expired for this week');
        console.log('⚠️ User needs to manually create a new plan for the current week');
        setIsPlanExpired(true);
        // Note: Don't auto-generate - let user decide when to create new plan
        // The existing plan will remain visible until user creates a new one
      } else {
        setIsPlanExpired(false);
      }
    };

    // Check immediately when component mounts or dependencies change
    checkWeekExpiration();

    // Check every hour for week expiration (but don't auto-generate)
    const interval = setInterval(checkWeekExpiration, 60 * 60 * 1000);

    return () => clearInterval(interval);
  }, [profile, weekPlan]);

  // Animation values for smooth transitions
  const cardScale = useSharedValue(1);
  const modalOpacity = useSharedValue(0);

  // REMOVED: planOpacity and animatePlanUpdate - these were causing flashing during updates

  // REMOVED: planAnimatedStyle - was causing flashing during updates
  // Plan container now uses static styling for stable rendering

  const availableTags = [
    { id: 'vegetarian', label: 'Vegetarian', icon: 'leaf' },
    { id: 'vegan', label: 'Vegan', icon: 'flower' },
    { id: 'keto', label: 'Keto', icon: 'flame' },
    { id: 'low-carb', label: 'Low Carb', icon: 'trending-down' },
    { id: 'high-protein', label: 'High Protein', icon: 'fitness' },
    { id: 'gluten-free', label: 'Gluten Free', icon: 'shield-checkmark' },
    { id: 'dairy-free', label: 'Dairy Free', icon: 'water' },
    { id: 'budget-friendly', label: 'Budget Friendly', icon: 'wallet' },
    { id: 'quick-meals', label: 'Quick Meals', icon: 'time' },
    { id: 'meal-prep', label: 'Meal Prep', icon: 'cube' },
    { id: 'anti-inflammatory', label: 'Anti-Inflammatory', icon: 'medical' },
    { id: 'heart-healthy', label: 'Heart Healthy', icon: 'heart' },
    { id: 'weight-loss', label: 'Weight Loss', icon: 'trending-down' },
    { id: 'muscle-gain', label: 'Muscle Gain', icon: 'barbell' },
    { id: 'energy-boost', label: 'Energy Boost', icon: 'flash' },
  ];

  // Generate meal plan function with customization
  const generateWeeklyPlan = async () => {
    try {
      // Check network connectivity first
      const isConnected = await ErrorHandlingService.checkNetworkConnectivity();
      if (!isConnected) {
        Alert.alert(
          'No Internet Connection',
          'Please check your internet connection and try again.',
          [{ text: 'OK', style: 'default' }]
        );
        return;
      }

      // Check if profile is available
      if (!profile) {
        Alert.alert(
          'Profile Not Available',
          'Please ensure your profile is loaded before generating meal plans.',
          [{ text: 'OK', style: 'default' }]
        );
        return;
      }

      setLoading(true);
      setLoadingType('plan'); // This will show "Generating meal plan with image preloading..."
    } catch (initError) {
      console.error('❌ Error initializing meal plan generation:', initError);
      Alert.alert(
        'Initialization Error',
        'Failed to initialize meal plan generation. Please try again.',
        [{ text: 'OK', style: 'default' }]
      );
      return;
    }

    try {
      // Build comprehensive goal string with user preferences
      let goal = 'Create a weekly meal plan with ONLY meal names';

      if (customPrompt.trim()) {
        goal += ` focusing on: ${customPrompt.trim()}`;
      } else {
        goal += ' for optimal health and nutrition';
      }

      if (selectedTags.length > 0) {
        const tagLabels = selectedTags.map(tagId =>
          availableTags.find(tag => tag.id === tagId)?.label || tagId
        );
        goal += `. Dietary preferences: ${tagLabels.join(', ')}`;
      }

      goal += `. Requirements:
      - Target ${calorieTarget} calories per day
      - Target ${proteinTarget}g protein daily
      - Plan for ${mealCount} meals per day
      - Maximum cooking time: ${cookingTime} minutes per meal
      - Budget level: ${budgetLevel}
      - Dietary style: ${dietaryStyle}
      - Activity level: ${activityLevel}`;

      if (cuisinePreferences.length > 0) {
        goal += `\n- Preferred cuisines: ${cuisinePreferences.join(', ')}`;
      }

      if (allergens.length > 0) {
        goal += `\n- CRITICAL: MUST AVOID these allergens: ${allergens.join(', ')}`;
      }

      if (healthGoals.length > 0) {
        goal += `\n- Health goals: ${healthGoals.join(', ')}`;
      }

      goal += `\n\nCRITICAL: Return ONLY meal names (2-4 words each). NO recipes, NO ingredients, NO cooking instructions.`;

      console.log('🍽️ Generating personalized meal plan...');

      // STEP 1: COMPLETELY DELETE EXISTING PLAN FROM STORAGE FIRST
      console.log('🗑️ Deleting existing meal plan from storage...');
      try {
        // Clear current plan from state
        setWeekPlan(null);

        // Delete from database
        await dbService.deleteWeeklyPlanFromDatabase();

        // Use WeeklyPlanManager's centralized clear method instead of manual AsyncStorage calls
        await WeeklyPlanManager.clearAllPlans();

        // Cancel existing meal reminder notifications
        if (notificationService) {
          await notificationService.cancelMealReminders();
        }

        console.log('✅ Existing meal plan completely deleted using WeeklyPlanManager');
      } catch (deleteError) {
        console.error('⚠️ Error deleting existing plan:', deleteError);
        // Continue with generation even if deletion fails
      }

      // STEP 2: GENERATE NEW PLAN using WeeklyPlanManager with image preloading
      try {
        console.log('🚀 Starting meal plan generation with image preloading...');
        const newPlan = await WeeklyPlanManager.generateWeeklyPlan(profile);

        if (newPlan) {
          setWeekPlan(newPlan);
          // REMOVED: setPlanRefreshKey - unnecessary re-render trigger

          console.log('✅ New weekly plan generated with all images preloaded');
          Alert.alert(
            'Plan Generated! 🎉',
            'Your personalized weekly meal plan is ready with all images preloaded for instant viewing.',
            [{ text: 'Great!', style: 'default' }]
          );
        } else {
          throw new Error('Failed to generate meal plan');
        }
      } catch (apiError) {
        console.error('API Error generating meal plan:', apiError);
        Alert.alert(
          'Meal Plan Generation Failed',
          'Unable to generate meal plan. Please check your internet connection and try again.',
          [
            { text: 'OK', style: 'default' },
            { text: 'Retry', onPress: () => generateWeeklyPlan() }
          ]
        );
        return;
      }
    } catch (error) {
      console.error('Error generating meal plan:', error);
      Alert.alert(
        'Error',
        'Failed to generate meal plan. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      // Add a small delay for smooth transition
      setTimeout(() => {
        setLoading(false);
        setLoadingType(null);
      }, 500);
    }
  };

  // Handle meal press for recipe details with smart caching
  const handleMealPress = async (mealType: string, meal: MealData) => {
    const startTime = performance.now();
    console.log(`🚀 PERFORMANCE: Starting on-demand recipe generation for: ${meal.name}`);

    try {
      const mealName = meal.name;
      setViewingRecipe(`${mealType}-${mealName}`);

      console.log(`🍽️ Opening recipe for: ${mealName}`);

      // Get or generate recipe using cache service (ON-DEMAND GENERATION)
      const cachedRecipe = await RecipeCacheService.getOrGenerateRecipe(mealName, 'weekly_plan');

      // Convert cached recipe to RecipeDetail format
      const recipeData = {
        id: cachedRecipe.id,
        title: cachedRecipe.title,
        description: cachedRecipe.description,
        image: cachedRecipe.imageUrl,
        imageUrl: cachedRecipe.imageUrl,
        cookTime: cachedRecipe.cookTime,
        servings: cachedRecipe.servings,
        difficulty: cachedRecipe.difficulty,
        calories: cachedRecipe.nutrition.calories,
        ingredients: cachedRecipe.ingredients,
        instructions: cachedRecipe.instructions,
        nutrition: {
          calories: cachedRecipe.nutrition.calories,
          protein: cachedRecipe.nutrition.protein,
          carbs: cachedRecipe.nutrition.carbs,
          fat: cachedRecipe.nutrition.fat,
          fiber: 5 // Default value
        },
        tags: cachedRecipe.tags
      };

      const endTime = performance.now();
      const totalTime = endTime - startTime;
      console.log(`🚀 PERFORMANCE: On-demand recipe generation completed in ${totalTime.toFixed(2)}ms`);
      console.log(`✅ Recipe ready for: ${mealName}`);

      // Navigate to recipe detail screen
      (navigation as any).navigate('RecipeDetail', {
        recipe: recipeData
      });

    } catch (error) {
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      console.error(`❌ Error loading recipe in ${totalTime.toFixed(2)}ms:`, error);
      Alert.alert(
        'Recipe Loading Failed',
        'Could not load recipe details. Please check your internet connection and try again.',
        [{ text: 'OK', style: 'default' }]
      );
    } finally {
      setViewingRecipe(null);
    }
  };

  // Get meal data from cached weekly plan (NO API CALLS)
  const getCachedMealData = (mealName: string, mealType: string, dayName: string) => {
    if (!weekPlan?.week) return null;

    const day = weekPlan.week.find(d => d.day.toLowerCase() === dayName.toLowerCase());
    if (!day) return null;

    const mealData = day.meals[mealType];
    if (!mealData) return null;

    console.log(`📊 Using cached data for: ${mealName}`);
    return {
      calories: mealData.calories,
      protein: mealData.protein,
      carbs: mealData.carbs,
      fat: mealData.fat,
      imageUrl: mealData.imageUrl,
      description: mealData.description
    };
  };

  // Mark meal as consumed using cached nutrition data (NO API CALLS) - PERFORMANCE OPTIMIZED
  const markMealAsConsumed = async (mealType: string, meal: MealData, dayName: string) => {
    try {
      const mealName = meal.name;

      // PERFORMANCE: Prevent multiple simultaneous logging operations using both state and ref
      if (isUpdatingPlan || updateOperationRef.current) {
        console.log('⚠️ Plan update in progress, skipping meal logging');
        return;
      }

      // Show loading state
      setLoadingType(`${mealType}-logging`);
      setIsUpdatingPlan(true);

      console.log(`🍽️ Logging meal with nutrition data: ${mealName}`);

      // Validate nutrition data before logging
      let nutrition = {
        calories: meal.calories,
        protein: meal.protein,
        carbs: meal.carbs,
        fat: meal.fat
      };

      // Check if nutrition data looks suspicious and needs regeneration
      const needsValidation = nutrition.calories === 350 || nutrition.protein < 10;
      if (needsValidation) {
        try {
          console.log(`🔄 Regenerating nutrition for suspicious meal: ${mealName}`);
          const freshRecipe = await RecipeCacheService.getOrGenerateRecipe(mealName, 'weekly_plan');
          nutrition = {
            calories: freshRecipe.nutrition.calories,
            protein: freshRecipe.nutrition.protein,
            carbs: freshRecipe.nutrition.carbs,
            fat: freshRecipe.nutrition.fat
          };
          console.log(`✅ Updated nutrition for ${mealName}:`, nutrition);
        } catch (error) {
          console.warn(`⚠️ Could not regenerate nutrition for ${mealName}, using existing data:`, error);
        }
      }

      // Add to recent meals with cached nutrition data
      await addRecentMeal({
        name: mealName,
        time: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
        calories: nutrition.calories,
        protein: nutrition.protein,
        carbs: nutrition.carbs,
        fat: nutrition.fat,
        type: mealType as 'breakfast' | 'lunch' | 'dinner' | 'snack',
        source: 'weekly_plan' // Mark as weekly plan sourced meal
      });

      // Log to WeightGoalTracker for progress tracking - WITH PROTEIN
      const today = new Date().toISOString().split('T')[0];
      await WeightGoalTracker.getInstance().logDailyNutrition(today, nutrition.calories, nutrition.protein, 'weekly_plan');

      // CRITICAL FIX: Trigger ProfileContext weight progress notification
      // This ensures HomeScreen weight goal updates immediately
      if (notifyWeightProgressUpdate) {
        notifyWeightProgressUpdate();
        console.log('🔄 Triggered weight progress update notification');
      } else {
        console.warn('⚠️ notifyWeightProgressUpdate not available');
      }

      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      Alert.alert(
        'Meal Logged! 🍽️',
        `${mealName} logged with ${nutrition.calories} calories, ${nutrition.protein}g protein.`,
        [{ text: 'Great!', style: 'default' }]
      );

      console.log(`✅ Successfully logged ${mealName} with cached nutrition:`, nutrition);

    } catch (error) {
      console.error('Error marking meal as consumed:', error);
      Alert.alert('Error', 'Could not log meal to nutrition tracking');
    } finally {
      setLoadingType(null);
      setIsUpdatingPlan(false);
    }
  };

  // State to track favorite status for meals
  const [mealFavorites, setMealFavorites] = useState<{[key: string]: boolean}>({});

  // State for cached meal images to prevent regeneration
  const [cachedMealImages, setCachedMealImages] = useState<{ [key: string]: string }>({});

  // Function to generate new image only when explicitly requested
  const generateNewMealImage = async (mealName: string, mealType: string) => {
    try {
      console.log(`🔄 User requested new image for: ${mealName}`);

      // Use RecipeCacheService to get or generate recipe (this will cache it properly)
      const cachedRecipe = await RecipeCacheService.getOrGenerateRecipe(mealName, 'weekly_plan');
      const newImageUrl = cachedRecipe.imageUrl || `https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=600&h=300&fit=crop&crop=center&q=80&t=${Date.now()}`;

      // Update cache with new image
      const mealKey = `${mealName}_${mealType}`;
      setCachedMealImages(prev => ({
        ...prev,
        [mealKey]: newImageUrl
      }));

      console.log(`✅ Generated new image for ${mealName}: ${newImageUrl}`);
      return newImageUrl;
    } catch (error) {
      console.error('❌ Failed to generate new image:', error);
      return null;
    }
  };

  // Load favorite status for all meals
  const loadMealFavoriteStatus = async () => {
    try {
      const favoriteStatuses: {[key: string]: boolean} = {};

      if (weekPlan && weekPlan.week) {
        for (const dayPlan of weekPlan.week) {
          for (const [mealType, mealName] of Object.entries(dayPlan.meals)) {
            const mealId = `${mealName}_${mealType}`;
            const isFavorited = await FavoritesService.isFavorited(mealId);
            favoriteStatuses[mealId] = isFavorited;
          }
        }
      }

      setMealFavorites(favoriteStatuses);
    } catch (error) {
      console.error('❌ Error loading meal favorite status:', error);
    }
  };

  // Load favorite status when weekly plan changes
  useEffect(() => {
    if (weekPlan && weekPlan.week && weekPlan.week.length > 0) {
      loadMealFavoriteStatus();
    }
  }, [weekPlan]);

  // Handle meal favorite toggle
  const handleMealFavoriteToggle = async (meal: any, mealType: string) => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      const mealId = `${meal.name}_${mealType}`;
      const favoriteRecipe = {
        id: mealId,
        name: meal.name,
        imageUrl: '', // Will be generated by the image service
        cookingTime: '30 min', // Default cooking time
        difficulty: 'Medium',
        calories: meal.calories,
        protein: 0, // Not available in meal plan data
        source: 'weekly_plan' as const
      };

      const success = await FavoritesService.toggleFavorite(favoriteRecipe);

      if (success) {
        const isFavorited = await FavoritesService.isFavorited(mealId);
        setMealFavorites(prev => ({
          ...prev,
          [mealId]: isFavorited
        }));
        console.log(`${isFavorited ? '❤️ Added to' : '💔 Removed from'} favorites: ${meal.name}`);
      }
    } catch (error) {
      console.error('❌ Error toggling meal favorite:', error);
    }
  };

  // Generate alternative meal using WeeklyPlanManager - PERFORMANCE OPTIMIZED
  const generateAlternative = async (mealType: string, currentMeal: MealData, dayName: string) => {
    // PERFORMANCE: Prevent multiple simultaneous updates using both state and ref
    if (isUpdatingPlan || updateOperationRef.current) {
      console.log('⚠️ Plan update in progress, skipping alternative generation');
      return;
    }

    // Create operation promise and store in ref to prevent race conditions
    const operationPromise = (async () => {
      setLoadingType(`${mealType}-alternative`);
      setIsUpdatingPlan(true);

      try {
        console.log(`🔄 Generating alternative for ${dayName} ${mealType}: "${currentMeal.name}"`);

        // Use WeeklyPlanManager to replace the meal
        const newMealData = await WeeklyPlanManager.replaceMealInCurrentPlan(dayName, mealType, profile);

        if (newMealData) {
          // PERFORMANCE: Single atomic state update to prevent flashing
          const updatedPlan = await WeeklyPlanManager.getActiveWeeklyPlan();
          if (updatedPlan) {
            console.log(`📊 Updated plan after replacement:`, updatedPlan.week.map(day => ({
              day: day.day,
              meals: Object.keys(day.meals)
            })));

            // Single atomic state update - no delays or animations that cause flashing
            setWeekPlan(updatedPlan);
          }

          console.log(`✅ Successfully replaced "${currentMeal.name}" with "${newMealData.name}" for ${dayName} ${mealType}`);
          Alert.alert('Alternative Generated', `Replaced "${currentMeal.name}" with "${newMealData.name}"`);
        } else {
          throw new Error('Failed to generate alternative meal');
        }

      } catch (error) {
        console.error('❌ Error generating alternative:', error);
        Alert.alert('Error', 'Failed to generate alternative meal. Please try again.');
      } finally {
        setLoadingType(null);
        setIsUpdatingPlan(false);
        updateOperationRef.current = null; // Clear operation ref
      }
    })();

    updateOperationRef.current = operationPromise;
    await operationPromise;
  };

  // Beautiful Modern Meal Card - Uses cached data from WeeklyPlanManager (NO API CALLS)
  const BeautifulMealCard: React.FC<{
    meal: MealData;
    type: string;
    mealType: string; // Add the actual meal key
    dayName: string;
    index: number;
  }> = ({ meal, type, mealType, dayName, index }) => {
    console.log(`🍽️ Rendering meal card for: ${meal.name} with cached data`);

    // All data comes from the cached meal plan - no API calls needed!

    return (
      <Animated.View
        entering={FadeInUp.delay(index * 100).duration(500)}
        style={styles.beautifulMealCard}
      >
        {/* Top: Beautiful Image with Overlay */}
        <TouchableOpacity
          onPress={() => handleMealPress(type, meal)}
          activeOpacity={0.95}
          style={styles.beautifulCardButton}
        >
          <View style={styles.beautifulImageSection}>
            <ImageBackground
              source={{
                uri: meal.imageUrl || 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=600&h=300&fit=crop&crop=center&q=80'
              }}
              style={styles.beautifulMealImage}
              imageStyle={styles.beautifulImageStyle}
            >
              <LinearGradient
                colors={['transparent', 'rgba(0,0,0,0.3)', 'rgba(0,0,0,0.7)']}
                style={styles.beautifulImageOverlay}
              >
                <View style={styles.beautifulTopRow}>
                  <View style={styles.beautifulMealBadge}>
                    <LottieIcon
                      name={type === 'Breakfast' ? 'apple' : type === 'Lunch' ? 'broccoli' : type === 'Dinner' ? 'chef' : 'avocado'}
                      size={14}
                      color={Colors.brandForeground}
                      enableHaptics={false}
                    />
                    <Text style={styles.beautifulMealType}>{type}</Text>
                  </View>
                  <View style={styles.beautifulTopRightActions}>
                    <TouchableOpacity
                      style={styles.beautifulFavoriteButton}
                      onPress={(e) => {
                        e.stopPropagation();
                        handleMealFavoriteToggle(meal, mealType);
                      }}
                    >
                      <Ionicons
                        name={mealFavorites[`${meal.name}_${mealType}`] ? "heart" : "heart-outline"}
                        size={16}
                        color={mealFavorites[`${meal.name}_${mealType}`] ? "#FF6B6B" : "#6B7C5A"}
                      />
                    </TouchableOpacity>
                    <View style={styles.beautifulCalorieBadge}>
                      <Text style={styles.beautifulCalorieText}>
                        {meal.calories} cal
                      </Text>
                    </View>
                  </View>
                </View>

                <View style={styles.beautifulBottomRow}>
                  <Text style={styles.beautifulMealName} numberOfLines={2}>{meal.name}</Text>
                  <View style={styles.beautifulMealMetaRow}>
                    <Text style={styles.beautifulMealTime}>{getMealInfo(mealType, index).time}</Text>
                    <Text style={styles.beautifulNutritionText}>
                      P: {meal.protein}g • C: {meal.carbs}g • F: {meal.fat}g
                    </Text>
                  </View>
                </View>
              </LinearGradient>
            </ImageBackground>
          </View>

          {/* Bottom: Action Buttons */}
          <View style={styles.beautifulActionsContainer}>
            {/* Top Row: View Recipe and Alternative */}
            <View style={styles.beautifulActionsTopRow}>
              <TouchableOpacity
                style={styles.beautifulPrimaryAction}
                onPress={(e) => {
                  e.stopPropagation();
                  handleMealPress(type, meal);
                }}
                disabled={viewingRecipe === `${type}-${meal.name}`}
              >
                {viewingRecipe === `${type}-${meal.name}` ? (
                  <>
                    <ActivityIndicator size="small" color="#fcf4ec" />
                    <Text style={styles.beautifulActionText}>Loading...</Text>
                  </>
                ) : (
                  <>
                    <Ionicons name="restaurant" size={16} color="#fcf4ec" />
                    <Text style={styles.beautifulActionText}>View Recipe</Text>
                  </>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.beautifulSecondaryAction}
                onPress={(e) => {
                  e.stopPropagation();
                  generateAlternative(mealType, meal, dayName);
                }}
                disabled={loadingType === `${type}-alternative`}
              >
                {loadingType === `${type}-alternative` ? (
                  <ActivityIndicator size="small" color="#6B7C5A" />
                ) : (
                  <>
                    <Ionicons name="refresh" size={16} color="#6B7C5A" />
                    <Text style={styles.beautifulSecondaryText}>Alternative</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>

            {/* Bottom Row: Log Meal (Full Width) */}
            <TouchableOpacity
              style={[
                styles.beautifulLogMealAction,
                loadingType === `${type}-logging` && styles.beautifulLogMealActionDisabled
              ]}
              onPress={(e) => {
                e.stopPropagation();
                markMealAsConsumed(mealType, meal, dayName);
              }}
              disabled={loadingType === `${type}-logging`}
            >
              {loadingType === `${type}-logging` ? (
                <>
                  <ActivityIndicator size="small" color="#6B7C5A" />
                  <Text style={styles.beautifulLogMealText}>Logging...</Text>
                </>
              ) : (
                <>
                  <Ionicons name="checkmark-circle" size={18} color="#6B7C5A" />
                  <Text style={styles.beautifulLogMealText}>Log Meal</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  // Beautiful Day Card with Clean Layout
  const BeautifulDayCard: React.FC<{ plan: any; delay: number }> = ({ plan, delay }) => {
    const isSelected = selectedDay === plan.day;

    return (
      <Animated.View entering={FadeInUp.delay(delay).duration(400)}>
        <View style={styles.beautifulDayCard}>
          {/* Clean Day Header */}
          <TouchableOpacity
            style={[styles.beautifulDayHeader, isSelected && styles.beautifulDayHeaderSelected]}
            onPress={() => setSelectedDay(isSelected ? null : plan.day)}
            activeOpacity={0.95}
          >
            <View style={styles.beautifulDayHeaderContent}>
              <View style={styles.beautifulDayInfo}>
                <Text style={[styles.beautifulDayName, isSelected && styles.beautifulDayNameSelected]}>
                  {plan.day}
                </Text>
                <Text style={[styles.beautifulDayMeta, isSelected && styles.beautifulDayMetaSelected]}>
                  {Object.keys(plan.meals).length} meals • 1,300 cal
                </Text>
              </View>
              <View style={[styles.beautifulDayToggle, isSelected && styles.beautifulDayToggleSelected]}>
                <Ionicons
                  name={isSelected ? "chevron-up" : "chevron-down"}
                  size={20}
                  color={isSelected ? "#fcf4ec" : "#6B7C5A"}
                />
              </View>
            </View>
          </TouchableOpacity>

          {/* Beautiful Meals List */}
          {isSelected && (
            <Animated.View entering={FadeInDown.duration(300)} style={styles.beautifulMealsList}>
              {Object.entries(plan.meals).map(([mealType, mealData], index) => {
                const mealInfo = getMealInfo(mealType, index);
                return (
                  <BeautifulMealCard
                    key={mealType}
                    meal={mealData as MealData}
                    type={mealInfo.type}
                    mealType={mealType}
                    dayName={plan.day}
                    index={index}
                  />
                );
              })}
            </Animated.View>
          )}
        </View>
      </Animated.View>
    );
  };

  return (
    <ImageBackground
      source={require('../../assets/screens background.jpg')}
      style={styles.backgroundImage}
      resizeMode="cover"
    >
      <View style={styles.overlay}>
        <StatusBar
        barStyle="dark-content"
        backgroundColor="transparent"
        translucent={Platform.OS === 'android'}
      />

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          showsVerticalScrollIndicator={false}
        >


          {/* Modern Apple-Inspired Header - Now Scrollable */}
          <SafeAreaView style={styles.modernAppleHeader}>
            <Animated.View entering={FadeInUp.duration(800)} style={styles.appleHeaderContent}>
              <View style={styles.appleHeaderTop}>
                <View style={styles.headerTextContainer}>
                  <Text style={styles.appleTitle}>Meal Plans</Text>
                  <Text style={styles.appleSubtitle}>Personalized nutrition made simple</Text>
                </View>
                <TouchableOpacity
                  style={styles.appleHeaderButton}
                  onPress={() => setShowCustomization(true)}
                >
                  <View style={styles.appleButtonInner}>
                    <Ionicons name="options" size={20} color="#6B7C5A" />
                  </View>
                </TouchableOpacity>
              </View>

              {/* Apple-style Stats Cards */}
              <Animated.View entering={SlideInUp.delay(200).duration(600)} style={styles.appleStatsContainer}>
                <View style={styles.appleStatCard}>
                  <Text style={styles.appleStatNumber}>7</Text>
                  <Text style={styles.appleStatLabel}>Days</Text>
                </View>
                <View style={styles.appleStatCard}>
                  <Text style={styles.appleStatNumber}>21</Text>
                  <Text style={styles.appleStatLabel}>Meals</Text>
                </View>
                <View style={styles.appleStatCard}>
                  <Text style={styles.appleStatNumber}>1.8k</Text>
                  <Text style={styles.appleStatLabel}>Avg Cal</Text>
                </View>
              </Animated.View>
            </Animated.View>
          </SafeAreaView>
        {/* Action Buttons */}
        {/* Apple-Style Action Cards */}
        <Animated.View entering={SlideInUp.delay(400).duration(600)} style={styles.appleActionSection}>
          <TouchableOpacity
            style={[styles.appleCreateCard, loading && styles.appleCreateCardDisabled]}
            onPress={generateWeeklyPlan}
            disabled={loading}
          >
            <View style={styles.appleCardContent}>
              <View style={styles.appleCardIcon}>
                {loading ? (
                  <ActivityIndicator size="small" color="#6B7C5A" />
                ) : (
                  <Ionicons name="add-circle" size={24} color="#6B7C5A" />
                )}
              </View>
              <View style={styles.appleCardText}>
                <Text style={styles.appleCardTitle}>
                  {loading ? 'Generating & Preloading Images...' : 'Create New Plan'}
                </Text>
                <Text style={styles.appleCardSubtitle}>
                  {loading ? 'AI-powered with instant image loading' : 'AI-powered meal planning'}
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#C7C7CC" />
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.appleCustomizeCard}
            onPress={() => setShowCustomization(true)}
          >
            <View style={styles.appleCardContent}>
              <View style={styles.appleCardIcon}>
                <Ionicons name="options" size={24} color="#6B7C5A" />
              </View>
              <View style={styles.appleCardText}>
                <Text style={styles.appleCardTitle}>Customize Preferences</Text>
                <Text style={styles.appleCardSubtitle}>
                  Dietary goals & restrictions
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#C7C7CC" />
            </View>
          </TouchableOpacity>
        </Animated.View>

        {/* Meal Plan Display */}
        {weekPlan ? (
          <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.modernPlanContainer}>
            {/* Loading overlay for smooth transitions */}
            {isUpdatingPlan && (
              <View style={styles.loadingOverlay}>
                <ActivityIndicator size="large" color="#6B7C5A" />
                <Text style={styles.loadingText}>Updating plan...</Text>
              </View>
            )}
            <View style={styles.applePlanHeader}>
              <Text style={styles.applePlanTitle}>
                {viewMode === 'week' ? 'This Week\'s Plan' : 'Today\'s Meals'}
              </Text>
              <View style={styles.appleToggleContainer}>
                <TouchableOpacity
                  style={[styles.appleToggleButton, viewMode === 'week' && styles.appleToggleButtonActive]}
                  onPress={() => setViewMode('week')}
                  activeOpacity={0.8}
                >
                  <Ionicons name="calendar" size={14} color={viewMode === 'week' ? '#fcf4ec' : '#6B7C5A'} />
                  <Text style={[styles.appleToggleText, viewMode === 'week' && styles.appleToggleTextActive]}>
                    Week
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.appleToggleButton, viewMode === 'day' && styles.appleToggleButtonActive]}
                  onPress={() => setViewMode('day')}
                  activeOpacity={0.8}
                >
                  <Ionicons name="today" size={14} color={viewMode === 'day' ? '#fcf4ec' : '#6B7C5A'} />
                  <Text style={[styles.appleToggleText, viewMode === 'day' && styles.appleToggleTextActive]}>
                    Day
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {viewMode === 'week' ? (
              weekPlan.week.map((dayPlan, index) => (
                <BeautifulDayCard key={dayPlan.day} plan={dayPlan} delay={index * 80} />
              ))
            ) : (
              // Day view - Beautiful clean layout for today's meals
              <Animated.View entering={FadeInUp.delay(200).duration(500)} style={styles.beautifulDayViewContainer}>
                <View style={styles.beautifulDayViewHeader}>
                  <Text style={styles.beautifulDayViewTitle}>Today's Menu</Text>
                  <Text style={styles.beautifulDayViewSubtitle}>Fresh meals curated for you</Text>
                </View>

                <View style={styles.beautifulDayViewMeals}>
                  {weekPlan.week.slice(0, 1).map((dayPlan) => (
                    <View key={dayPlan.day} style={styles.beautifulMealsList}>
                      {Object.entries(dayPlan.meals).map(([mealType, mealData], index) => {
                        const mealInfo = getMealInfo(mealType, index);
                        return (
                          <BeautifulMealCard
                            key={mealType}
                            meal={mealData as MealData}
                            type={mealInfo.type}
                            mealType={mealType}
                            dayName={dayPlan.day}
                            index={index}
                          />
                        );
                      })}
                    </View>
                  ))}
                </View>
              </Animated.View>
            )}
          </Animated.View>
        ) : (
          <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.modernEmptyState}>
            <LinearGradient
              colors={['rgba(107, 124, 90, 0.1)', 'rgba(107, 124, 90, 0.05)']}
              style={styles.emptyStateGradient}
            >
              <Ionicons
                name={isPlanExpired ? "time-outline" : "restaurant-outline"}
                size={80}
                color={isPlanExpired ? "#F59E0B" : "#6B7C5A"}
              />
              <Text style={styles.modernEmptyTitle}>
                {isPlanExpired ? "Plan Expired" : "Ready to Plan Your Week?"}
              </Text>
              <Text style={styles.modernEmptySubtitle}>
                {isPlanExpired
                  ? "Your current meal plan is for a previous week. Create a new plan for this week."
                  : "Create a personalized meal plan tailored to your goals and preferences"
                }
              </Text>
              <TouchableOpacity
                style={styles.emptyStateButton}
                onPress={generateWeeklyPlan}
              >
                <LinearGradient
                  colors={['#6B7C5A', '#8B9A7A']}
                  style={styles.emptyButtonGradient}
                >
                  {loading ? (
                    <>
                      <ActivityIndicator size="small" color="#fcf4ec" />
                      <Text style={styles.emptyButtonText}>Creating Plan...</Text>
                    </>
                  ) : (
                    <>
                      <Ionicons name="add-circle" size={20} color="#fcf4ec" />
                      <Text style={styles.emptyButtonText}>
                        {isPlanExpired ? "Create New Plan" : "Create Meal Plan"}
                      </Text>
                    </>
                  )}
                </LinearGradient>
              </TouchableOpacity>
            </LinearGradient>
          </Animated.View>
        )}
      </ScrollView>

      {/* Simple Working Customize Modal */}
      <Modal
        visible={showCustomization}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCustomization(false)}
      >
        <View style={styles.simpleModalOverlay}>
          <View style={styles.simpleModalContainer}>
            {/* Header */}
            <View style={styles.simpleModalHeader}>
              <Text style={styles.simpleModalTitle}>🎯 Customize Your Plan</Text>
              <TouchableOpacity
                style={styles.simpleModalClose}
                onPress={() => setShowCustomization(false)}
              >
                <Ionicons name="close" size={24} color="#6B7C5A" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.simpleModalContent}>
              {/* Goals */}
              <View style={styles.simpleSection}>
                <Text style={styles.simpleSectionTitle}>Your Goals</Text>
                <TextInput
                  style={styles.simpleTextInput}
                  placeholder="e.g., lose weight, build muscle, improve energy..."
                  value={customPrompt}
                  onChangeText={setCustomPrompt}
                  multiline
                />
              </View>

              {/* Nutrition */}
              <View style={styles.simpleSection}>
                <Text style={styles.simpleSectionTitle}>Nutrition Targets</Text>
                <View style={styles.simpleRow}>
                  <View style={styles.simpleInputGroup}>
                    <Text style={styles.simpleLabel}>Calories</Text>
                    <TextInput
                      style={styles.simpleNumberInput}
                      value={calorieTarget.toString()}
                      onChangeText={(text) => setCalorieTarget(parseInt(text) || 2000)}
                      keyboardType="numeric"
                    />
                  </View>
                  <View style={styles.simpleInputGroup}>
                    <Text style={styles.simpleLabel}>Protein (g)</Text>
                    <TextInput
                      style={styles.simpleNumberInput}
                      value={proteinTarget.toString()}
                      onChangeText={(text) => setProteinTarget(parseInt(text) || 150)}
                      keyboardType="numeric"
                    />
                  </View>
                </View>
              </View>

              {/* Dietary Tags */}
              <View style={styles.simpleSection}>
                <Text style={styles.simpleSectionTitle}>Dietary Preferences</Text>
                <View style={styles.simpleTagsContainer}>
                  {availableTags.slice(0, 8).map((tag) => (
                    <TouchableOpacity
                      key={tag.id}
                      style={[
                        styles.simpleTag,
                        selectedTags.includes(tag.id) && styles.simpleTagSelected
                      ]}
                      onPress={() => {
                        setSelectedTags(prev =>
                          prev.includes(tag.id)
                            ? prev.filter(t => t !== tag.id)
                            : [...prev, tag.id]
                        );
                      }}
                    >
                      <Text style={[
                        styles.simpleTagText,
                        selectedTags.includes(tag.id) && styles.simpleTagTextSelected
                      ]}>
                        {tag.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Meal Count */}
              <View style={styles.simpleSection}>
                <Text style={styles.simpleSectionTitle}>Meals per day</Text>
                <View style={styles.simpleCountContainer}>
                  {[3, 4, 5, 6].map((count) => (
                    <TouchableOpacity
                      key={count}
                      style={[
                        styles.simpleCountButton,
                        mealCount === count && styles.simpleCountButtonSelected
                      ]}
                      onPress={() => setMealCount(count)}
                    >
                      <Text style={[
                        styles.simpleCountText,
                        mealCount === count && styles.simpleCountTextSelected
                      ]}>
                        {count}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </ScrollView>

            {/* Actions */}
            <View style={styles.simpleModalActions}>
              <TouchableOpacity
                style={styles.simpleCancelButton}
                onPress={() => setShowCustomization(false)}
              >
                <Text style={styles.simpleCancelText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.simpleSaveButton}
                onPress={() => {
                  setShowCustomization(false);
                  generateWeeklyPlan();
                }}
              >
                <Text style={styles.simpleSaveText}>Generate Plan</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      </View>
    </ImageBackground>
  );
};

const styles = {
  // Background Image with Overlay
  backgroundImage: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(252, 244, 236, 0.85)', // 85% opacity to show background images
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingTop: 60, // Reduced by 20px
    paddingBottom: 120, // Proper bottom spacing after content
  },

  // Apple-Inspired Header - Now Scrollable
  modernAppleHeader: {
    backgroundColor: 'rgba(252, 244, 236, 0.95)', // Slightly more opaque for header readability
    paddingBottom: 24,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  appleHeaderContent: {
    paddingHorizontal: 20,
    paddingTop: 8,
  },
  appleHeaderTop: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'flex-start' as const,
    marginBottom: 24,
  },
  headerTextContainer: {
    flex: 1,
  },
  appleTitle: {
    fontSize: 34,
    fontWeight: '700' as const,
    color: '#1D1D1F',
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  appleSubtitle: {
    fontSize: 17,
    color: '#86868B',
    fontWeight: '400' as const,
  },
  appleHeaderButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#F2F2F7',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  appleButtonInner: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#fcf4ec',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // Apple Stats Cards
  appleStatsContainer: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  appleStatCard: {
    flex: 1,
    backgroundColor: '#fcf4ec',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  appleStatNumber: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: '#6B7C5A',
    marginBottom: 4,
  },
  appleStatLabel: {
    fontSize: 13,
    color: '#86868B',
    fontWeight: '500' as const,
  },

  // Apple Action Section
  appleActionSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    marginHorizontal: 16,
    marginTop: 16,
    backgroundColor: '#fcf4ec',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
    gap: 12,
  },
  appleCreateCard: {
    backgroundColor: '#fcf4ec',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  appleCreateCardDisabled: {
    opacity: 0.6,
  },
  appleCustomizeCard: {
    backgroundColor: '#fcf4ec',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  appleCardContent: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    padding: 20,
  },
  appleCardIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#F2F2F7',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    marginRight: 16,
  },
  appleCardText: {
    flex: 1,
  },
  appleCardTitle: {
    fontSize: 17,
    fontWeight: '600' as const,
    color: '#1D1D1F',
    marginBottom: 2,
  },
  appleCardSubtitle: {
    fontSize: 15,
    color: '#86868B',
    fontWeight: '400' as const,
  },

  // Apple Plan Section - Responsive
  applePlanHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 20,
    flexWrap: 'wrap' as const,
    gap: 12,
  },
  applePlanTitle: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: '#1D1D1F',
    letterSpacing: -0.5,
    flex: 1,
    minWidth: 200,
  },
  appleToggleContainer: {
    flexDirection: 'row' as const,
    backgroundColor: '#F2F2F7',
    borderRadius: 10,
    padding: 2,
    minWidth: 120,
  },
  appleToggleButton: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 8,
    gap: 4,
    flex: 1,
    justifyContent: 'center' as const,
  },
  appleToggleButtonActive: {
    backgroundColor: '#6B7C5A',
  },
  appleToggleText: {
    fontSize: 12,
    fontWeight: '600' as const,
    color: '#6B7C5A',
  },
  appleToggleTextActive: {
    color: '#fcf4ec',
  },

  // Stunning Day View Styles
  stunningDayViewContainer: {
    marginBottom: 20,
  },
  stunningDayViewHeader: {
    borderRadius: 20,
    overflow: 'hidden' as const,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  stunningDayViewHeaderGradient: {
    padding: 24,
    alignItems: 'center' as const,
  },
  stunningDayViewTitle: {
    fontSize: 28,
    fontWeight: '800' as const,
    color: '#fcf4ec',
    marginBottom: 8,
    textAlign: 'center' as const,
  },
  stunningDayViewSubtitle: {
    fontSize: 16,
    color: '#fcf4ec',
    fontWeight: '400' as const,
    textAlign: 'center' as const,
  },
  stunningDayViewMeals: {
    gap: 16,
  },

  // Stunning Day Card Styles
  stunningDayCard: {
    marginBottom: 20,
    borderRadius: 20,
    overflow: 'hidden' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 10,
    backgroundColor: '#fcf4ec',
  },
  stunningDayHeader: {
    borderRadius: 20,
    overflow: 'hidden' as const,
  },
  stunningDayHeaderSelected: {
    // No additional styles needed
  },
  stunningDayHeaderGradient: {
    padding: 20,
  },
  stunningDayHeaderContent: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  stunningDayInfo: {
    flex: 1,
  },
  stunningDayName: {
    fontSize: 24,
    fontWeight: '800' as const,
    color: '#1D1D1F',
    marginBottom: 6,
  },
  stunningDayNameSelected: {
    color: '#fcf4ec',
  },
  stunningDayMeta: {
    fontSize: 16,
    color: '#86868B',
    fontWeight: '500' as const,
  },
  stunningDayMetaSelected: {
    color: '#fcf4ec',
  },
  stunningDayAction: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  stunningMealsList: {
    padding: 20,
    gap: 16,
    backgroundColor: '#F8F9FA',
  },

  // Stunning Meal Card Styles - Full Width, 1 per row
  stunningMealCard: {
    backgroundColor: '#fcf4ec',
    borderRadius: 20,
    overflow: 'hidden' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    marginBottom: 16,
  },
  stunningCardButton: {
    flexDirection: 'row' as const,
    padding: 0,
  },
  stunningImageContainer: {
    width: 140,
    height: 120,
  },
  stunningMealImage: {
    width: '100%' as const,
    height: '100%' as const,
    justifyContent: 'flex-end' as const,
  },
  stunningImageStyle: {
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
  },
  stunningImageOverlay: {
    flex: 1,
    justifyContent: 'space-between' as const,
    padding: 12,
  },
  stunningMealBadge: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 6,
    backgroundColor: '#fcf4ec',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start' as const,
  },
  stunningMealType: {
    fontSize: 12,
    fontWeight: '700' as const,
    color: '#fcf4ec',
  },
  stunningContentContainer: {
    flex: 1,
    padding: 16,
    justifyContent: 'space-between' as const,
  },
  stunningMealInfo: {
    flex: 1,
  },
  stunningMealName: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: '#1D1D1F',
    marginBottom: 8,
    lineHeight: 22,
  },
  stunningMealMeta: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 16,
  },
  stunningTimeContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 4,
  },
  stunningMealTime: {
    fontSize: 14,
    color: '#86868B',
    fontWeight: '500' as const,
  },
  stunningCalorieContainer: {
    flexDirection: 'row' as const,
    alignItems: 'baseline' as const,
    gap: 2,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  stunningMealCalories: {
    fontSize: 16,
    fontWeight: '700' as const,
    color: '#6B7C5A',
  },
  stunningCalorieLabel: {
    fontSize: 12,
    color: '#6B7C5A',
    fontWeight: '500' as const,
  },
  stunningActions: {
    flexDirection: 'row' as const,
    gap: 8,
  },
  stunningPrimaryAction: {
    backgroundColor: '#6B7C5A',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 10,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 6,
    flex: 1,
    justifyContent: 'center' as const,
  },
  stunningActionText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: '#fcf4ec',
  },
  stunningSecondaryAction: {
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 10,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },

  // Removed bottom safe area - using scrollView paddingBottom instead

  // Beautiful Day View Styles - Clean & Modern
  beautifulDayViewContainer: {
    gap: 20,
  },
  beautifulDayViewHeader: {
    backgroundColor: '#fcf4ec',
    borderRadius: 20,
    padding: 24,
    alignItems: 'center' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  beautifulDayViewTitle: {
    fontSize: 26,
    fontWeight: '800' as const,
    color: '#1D1D1F',
    marginBottom: 6,
    textAlign: 'center' as const,
  },
  beautifulDayViewSubtitle: {
    fontSize: 16,
    color: '#86868B',
    fontWeight: '400' as const,
    textAlign: 'center' as const,
  },
  beautifulDayViewMeals: {
    gap: 16,
  },

  // Beautiful Day Card Styles - Clean & Simple
  beautifulDayCard: {
    backgroundColor: '#fcf4ec',
    borderRadius: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 10,
    elevation: 5,
    overflow: 'hidden' as const,
  },
  beautifulDayHeader: {
    backgroundColor: '#fcf4ec',
    padding: 20,
  },
  beautifulDayHeaderSelected: {
    backgroundColor: '#6B7C5A',
  },
  beautifulDayHeaderContent: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  beautifulDayInfo: {
    flex: 1,
  },
  beautifulDayName: {
    fontSize: 22,
    fontWeight: '700' as const,
    color: '#1D1D1F',
    marginBottom: 4,
  },
  beautifulDayNameSelected: {
    color: '#fcf4ec',
  },
  beautifulDayMeta: {
    fontSize: 15,
    color: '#86868B',
    fontWeight: '500' as const,
  },
  beautifulDayMetaSelected: {
    color: '#fcf4ec',
  },
  beautifulDayToggle: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  beautifulDayToggleSelected: {
    backgroundColor: '#fcf4ec',
  },
  beautifulMealsList: {
    padding: 16,
    gap: 16,
    backgroundColor: '#F8F9FA',
  },

  // Beautiful Meal Card Styles - Clean & Modern
  beautifulMealCard: {
    backgroundColor: '#fcf4ec',
    borderRadius: 20,
    overflow: 'hidden' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.08,
    shadowRadius: 10,
    elevation: 5,
    marginBottom: 16,
  },
  beautifulCardButton: {
    // No additional styles needed
  },
  beautifulImageSection: {
    height: 180,
    position: 'relative' as const,
  },
  beautifulMealImage: {
    width: '100%' as const,
    height: '100%' as const,
    justifyContent: 'space-between' as const,
  },
  beautifulImageStyle: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  beautifulImageOverlay: {
    flex: 1,
    justifyContent: 'space-between' as const,
    padding: 16,
  },
  beautifulTopRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'flex-start' as const,
  },
  beautifulTopRightActions: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 8,
  },
  beautifulFavoriteButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#fcf4ec',
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    borderWidth: 1,
    borderColor: '#8B9A7A',
  },
  beautifulMealBadge: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 6,
    backgroundColor: Colors.brand,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
  },
  beautifulMealType: {
    fontSize: 13,
    fontWeight: '700' as const,
    color: Colors.brandForeground,
  },
  beautifulCalorieBadge: {
    backgroundColor: '#fcf4ec',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  beautifulCalorieText: {
    fontSize: 12,
    fontWeight: '600' as const,
    color: Colors.foreground,
  },
  beautifulBottomRow: {
    gap: 4,
  },
  beautifulMealName: {
    fontSize: 20,
    fontWeight: '700' as const,
    color: '#fcf4ec',
    lineHeight: 24,
  },
  beautifulMealMetaRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    gap: 8,
  },
  beautifulMealTime: {
    fontSize: 14,
    color: '#fcf4ec',
    fontWeight: '500' as const,
  },
  beautifulNutritionText: {
    fontSize: 12,
    color: '#fcf4ec',
    fontWeight: '500' as const,
  },
  beautifulActionsSection: {
    padding: 16,
    flexDirection: 'row' as const,
    gap: 12,
  },
  beautifulPrimaryAction: {
    backgroundColor: '#6B7C5A',
    borderRadius: 14,
    paddingHorizontal: 20,
    paddingVertical: 12,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 8,
    flex: 1,
    justifyContent: 'center' as const,
  },
  beautifulActionText: {
    fontSize: 13, // Smaller text
    fontWeight: '600' as const,
    color: '#fcf4ec',
  },
  beautifulSecondaryAction: {
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 14,
    paddingHorizontal: 16, // Slightly smaller padding
    paddingVertical: 12,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 6, // Smaller gap
    flex: 1,
    justifyContent: 'center' as const,
  },
  beautifulSecondaryText: {
    fontSize: 12, // Much smaller text for Alternative
    fontWeight: '600' as const,
    color: '#6B7C5A',
  },
  beautifulConsumedAction: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderRadius: 14,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 6,
    flex: 1,
    justifyContent: 'center' as const,
  },
  beautifulConsumedText: {
    fontSize: 12,
    fontWeight: '600' as const,
    color: '#4CAF50',
  },

  // New Action Layout Styles
  beautifulActionsContainer: {
    padding: 16,
    gap: 12,
  },
  beautifulActionsTopRow: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  beautifulLogMealAction: {
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 16,
    paddingVertical: 14,
    paddingHorizontal: 20,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    gap: 8,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.2)',
  },
  beautifulLogMealActionDisabled: {
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    opacity: 0.7,
  },
  beautifulLogMealText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: '#6B7C5A',
  },

  // Modern Day Card with Grid Layout
  modernDayCard: {
    marginBottom: 20,
    backgroundColor: '#fcf4ec',
    borderRadius: 20,
    overflow: 'hidden' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  dayCardHeader: {
    backgroundColor: '#fcf4ec',
    padding: 20,
  },
  dayCardHeaderSelected: {
    backgroundColor: '#6B7C5A',
  },
  dayHeaderContent: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  dayInfo: {
    flex: 1,
  },
  modernDayName: {
    fontSize: 22,
    fontWeight: '700' as const,
    color: '#1D1D1F',
    marginBottom: 4,
  },
  modernDayNameSelected: {
    color: '#fcf4ec',
  },
  dayMealCount: {
    fontSize: 15,
    color: '#86868B',
    fontWeight: '500' as const,
  },
  dayMealCountSelected: {
    color: '#fcf4ec',
  },
  modernDayMealsGrid: {
    padding: 16,
    backgroundColor: '#F8F9FA',
  },
  mealsGridContainer: {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    marginHorizontal: -6,
  },

  // Large Modern Meal Card with Real Images
  modernMealCard: {
    backgroundColor: '#fcf4ec',
    borderRadius: 20,
    overflow: 'hidden' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
    marginBottom: 16,
  },
  mealCardContent: {
    padding: 0,
  },
  mealImageContainer: {
    width: '100%' as const,
    height: 200,
    position: 'relative' as const,
  },
  mealImage: {
    width: '100%' as const,
    height: '100%' as const,
    justifyContent: 'space-between' as const,
  },
  mealImageStyle: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  mealImageOverlay: {
    flex: 1,
    justifyContent: 'space-between' as const,
    padding: 16,
  },
  imageLoadingContainer: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  mealTypeContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 6,
    backgroundColor: '#fcf4ec',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    alignSelf: 'flex-start' as const,
  },
  mealTypeText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: '#fcf4ec',
  },
  mealCaloriesOverlay: {
    fontSize: 16,
    fontWeight: '700' as const,
    color: '#fcf4ec',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    alignSelf: 'flex-end' as const,
  },
  mealInfo: {
    padding: 20,
  },
  modernMealName: {
    fontSize: 20,
    fontWeight: '700' as const,
    color: '#1D1D1F',
    marginBottom: 8,
    lineHeight: 24,
  },
  modernMealTime: {
    fontSize: 16,
    color: '#86868B',
    marginBottom: 16,
  },
  mealActions: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  primaryAction: {
    backgroundColor: '#6B7C5A',
    borderRadius: 12,
    paddingHorizontal: 20,
    paddingVertical: 12,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 8,
    flex: 1,
    justifyContent: 'center' as const,
  },
  actionText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#fcf4ec',
  },
  secondaryAction: {
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 20,
    paddingVertical: 12,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 8,
    flex: 1,
    justifyContent: 'center' as const,
  },
  secondaryActionText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#6B7C5A',
  },

  // Compact Meal Card Styles for Modern Grid Layout
  compactMealCard: {
    flex: 1,
    margin: 6,
    borderRadius: 16,
    overflow: 'hidden' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
    backgroundColor: '#fcf4ec',
  },
  compactCardButton: {
    flex: 1,
  },
  compactMealImage: {
    width: '100%' as const,
    height: 160,
    justifyContent: 'space-between' as const,
  },
  compactImageStyle: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  compactImageOverlay: {
    flex: 1,
    justifyContent: 'space-between' as const,
    padding: 12,
  },
  compactMealBadge: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 4,
    backgroundColor: '#fcf4ec',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start' as const,
  },
  compactMealType: {
    fontSize: 12,
    fontWeight: '600' as const,
    color: '#fcf4ec',
  },
  compactMealInfo: {
    gap: 4,
  },
  compactMealName: {
    fontSize: 16,
    fontWeight: '700' as const,
    color: '#fcf4ec',
    lineHeight: 20,
  },
  compactMealMeta: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  compactMealTime: {
    fontSize: 12,
    color: '#fcf4ec',
    fontWeight: '500' as const,
  },
  compactMealCalories: {
    fontSize: 12,
    color: '#fcf4ec',
    fontWeight: '700' as const,
    backgroundColor: '#fcf4ec',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  compactActions: {
    flexDirection: 'row' as const,
    padding: 8,
    gap: 8,
  },
  compactPrimaryAction: {
    flex: 1,
    backgroundColor: '#6B7C5A',
    borderRadius: 8,
    paddingVertical: 8,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  compactSecondaryAction: {
    flex: 1,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 8,
    paddingVertical: 8,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },

  // Quick Stats
  quickStats: {
    flexDirection: 'row' as const,
    backgroundColor: '#fcf4ec',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center' as const,
    justifyContent: 'space-around' as const,
  },
  statItem: {
    alignItems: 'center' as const,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: '#fcf4ec',
  },
  statLabel: {
    fontSize: 12,
    color: '#fcf4ec',
    marginTop: 2,
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: '#fcf4ec',
  },

  // Action Section
  actionSection: {
    padding: 20,
    paddingTop: 0,
    backgroundColor: 'rgba(107, 124, 90, 0.03)',
  },
  actionGrid: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  modernCreateButton: {
    flex: 2,
    borderRadius: 20,
    overflow: 'hidden' as const,
    backgroundColor: '#6B7C5A',
  },
  modernCreateButtonDisabled: {
    opacity: 0.7,
  },
  createButtonGradient: {
    padding: 20,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    flexDirection: 'row' as const,
    gap: 8,
    backgroundColor: '#6B7C5A',
  },
  modernCreateText: {
    fontSize: 16,
    fontWeight: '700' as const,
    color: '#fcf4ec',
  },
  modernCustomizeButton: {
    flex: 1,
    backgroundColor: '#8B9A7A',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  customizeButtonContent: {
    alignItems: 'center' as const,
    gap: 4,
  },
  customizeText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: '#fcf4ec',
  },

  // Plan Container
  modernPlanContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    marginHorizontal: 16,
    marginTop: 16,
    backgroundColor: '#fcf4ec',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  planHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 20,
  },
  modernPlanTitle: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: '#1F2937',
  },
  viewModeToggle: {
    flexDirection: 'row' as const,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 12,
    padding: 4,
  },
  toggleButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  toggleButtonActive: {
    backgroundColor: '#6B7C5A',
  },

  // Removed duplicate day card styles - using new ones above

  // Removed duplicate meal card styles - using new ones above

  // Empty State
  modernEmptyState: {
    margin: 20,
    borderRadius: 24,
    overflow: 'hidden' as const,
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
  },
  emptyStateGradient: {
    padding: 40,
    alignItems: 'center' as const,
  },
  modernEmptyTitle: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: '#1F2937',
    marginTop: 20,
    marginBottom: 8,
  },
  modernEmptySubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center' as const,
    lineHeight: 24,
    marginBottom: 24,
  },
  emptyStateButton: {
    borderRadius: 16,
    overflow: 'hidden' as const,
    backgroundColor: '#6B7C5A',
  },
  emptyButtonGradient: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 8,
    backgroundColor: '#6B7C5A',
  },
  emptyButtonText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#fcf4ec',
  },

  // Simple Header
  simpleHeader: {
    backgroundColor: '#6B7C5A',
    paddingBottom: 20,
  },

  // Simple Modal Styles
  simpleModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    padding: 20,
  },
  simpleModalContainer: {
    width: '100%' as const,
    height: height * 0.85,
    backgroundColor: '#fcf4ec',
    borderRadius: 20,
    overflow: 'hidden' as const,
    marginTop: 60,
  },
  simpleModalHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  simpleModalTitle: {
    fontSize: 20,
    fontWeight: '700' as const,
    color: '#1F2937',
  },
  simpleModalClose: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  simpleModalContent: {
    flex: 1,
    padding: 20,
  },
  simpleSection: {
    marginBottom: 24,
  },
  simpleSectionTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#1F2937',
    marginBottom: 12,
  },
  simpleTextInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#1F2937',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    minHeight: 80,
    textAlignVertical: 'top' as const,
  },
  simpleRow: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  simpleInputGroup: {
    flex: 1,
  },
  simpleLabel: {
    fontSize: 14,
    fontWeight: '500' as const,
    color: '#6B7280',
    marginBottom: 8,
  },
  simpleNumberInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    color: '#1F2937',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    textAlign: 'center' as const,
  },
  simpleTagsContainer: {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    gap: 8,
  },
  simpleTag: {
    backgroundColor: '#F3F4F6',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  simpleTagSelected: {
    backgroundColor: '#6B7C5A',
  },
  simpleTagText: {
    fontSize: 14,
    fontWeight: '500' as const,
    color: '#6B7C5A',
  },
  simpleTagTextSelected: {
    color: '#fcf4ec',
  },
  simpleCountContainer: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  simpleCountButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F3F4F6',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  simpleCountButtonSelected: {
    backgroundColor: '#6B7C5A',
  },
  simpleCountText: {
    fontSize: 18,
    fontWeight: '600' as const,
    color: '#6B7C5A',
  },
  simpleCountTextSelected: {
    color: '#fcf4ec',
  },
  simpleModalActions: {
    flexDirection: 'row' as const,
    padding: 20,
    paddingTop: 16,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  simpleCancelButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  simpleCancelText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#6B7280',
  },
  simpleSaveButton: {
    flex: 2,
    backgroundColor: '#6B7C5A',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  simpleSaveText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#fcf4ec',
  },
  modalGradient: {
    flex: 1,
  },
  modernModalHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'flex-start' as const,
    padding: 24,
    paddingBottom: 16,
  },
  modalHeaderContent: {
    flex: 1,
  },
  modernModalTitle: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: '#1F2937',
    marginBottom: 4,
  },
  modernModalSubtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  modernModalClose: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  modernModalContent: {
    flex: 1,
    paddingHorizontal: 24,
  },
  modernSection: {
    marginBottom: 24,
  },
  modernSectionTitle: {
    fontSize: 18,
    fontWeight: '600' as const,
    color: '#1F2937',
    marginBottom: 12,
  },
  modernTextInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#1F2937',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    minHeight: 80,
    textAlignVertical: 'top' as const,
  },
  nutritionGrid: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  nutritionItem: {
    flex: 1,
  },
  nutritionLabel: {
    fontSize: 14,
    fontWeight: '500' as const,
    color: '#6B7280',
    marginBottom: 8,
  },
  nutritionInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    color: '#1F2937',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    textAlign: 'center' as const,
  },
  modernTagsGrid: {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    gap: 8,
  },
  modernTag: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    backgroundColor: '#F3F4F6',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 6,
  },
  modernTagSelected: {
    backgroundColor: '#6B7C5A',
  },
  modernTagText: {
    fontSize: 14,
    fontWeight: '500' as const,
    color: '#6B7C5A',
  },
  modernTagTextSelected: {
    color: '#fcf4ec',
  },
  preferenceRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 16,
  },
  preferenceLabel: {
    fontSize: 16,
    fontWeight: '500' as const,
    color: '#1F2937',
  },
  modernCountButtons: {
    flexDirection: 'row' as const,
    gap: 8,
  },
  modernCountButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  modernCountButtonSelected: {
    backgroundColor: '#6B7C5A',
  },
  modernCountText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#6B7C5A',
  },
  modernCountTextSelected: {
    color: '#fcf4ec',
  },
  modernModalActions: {
    flexDirection: 'row' as const,
    padding: 24,
    paddingTop: 16,
    gap: 12,
  },
  modernCancelButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  modernCancelText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#6B7280',
  },
  modernSaveButton: {
    flex: 2,
    borderRadius: 16,
    overflow: 'hidden' as const,
    backgroundColor: '#6B7C5A',
  },
  saveButtonGradient: {
    paddingVertical: 16,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    backgroundColor: '#6B7C5A',
  },
  modernSaveText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#fcf4ec',
  },

  // Loading overlay styles for smooth transitions
  loadingOverlay: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(252, 244, 236, 0.9)',
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    zIndex: 1000,
    borderRadius: 32,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    fontWeight: '500' as const,
    color: '#6B7C5A',
  },
};

export default PlanScreenModern;
