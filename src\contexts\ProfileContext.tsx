import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { getDefaultProfile, UserProfile as ImportedUserProfile } from '../constants/UserData';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DatabaseIntegrationService from '../services/DatabaseIntegrationService';
import NotificationService from '../services/NotificationService';
import { WeightGoalTracker } from '../services/WeightGoalTracker';
import WeeklyPlanManager from '../services/WeeklyPlanManager';

// Use the interface from UserData.ts
export type UserProfile = ImportedUserProfile;

// Meal interface for detailed tracking
export interface MealEntry {
  id: string;
  name: string;
  time: string;
  calories: number;
  protein?: number; // Add protein tracking
  carbs?: number;   // Add carbs tracking
  fat?: number;     // Add fat tracking
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  timestamp: number;
  imageUri?: string; // Add image URI for scanned meals
  source?: 'scanner' | 'weekly_plan' | 'manual'; // Track meal source
}

// Daily tracking data interface
export interface DailyData {
  date: string;
  caloriesConsumed: number;
  proteinConsumed: number;
  waterConsumed: number;
  stepsCompleted: number;
  workoutsCompleted: number;
  mealsLogged: string[];
  recentMeals: MealEntry[];
  weight?: number;
  mood?: string;
  energyLevel?: number; // 1-10
  sleepHours?: number;
}

// Context interface
interface ProfileContextType {
  profile: UserProfile;
  dailyData: DailyData;
  isProfileLoaded: boolean;
  updateProfile: (field: keyof UserProfile, value: any) => Promise<void>;
  updateProfileBatch: (updates: Partial<UserProfile>) => Promise<void>;
  updateDailyData: (field: keyof DailyData, value: any) => void;
  refreshProfile: () => Promise<void>;
  calculateBMI: () => number;
  calculateCaloriesRemaining: () => number;
  calculateProteinRemaining: () => number;
  calculateWaterRemaining: () => number;
  calculateStepsRemaining: () => number;
  getProgressPercentage: (type: 'calories' | 'protein' | 'water' | 'steps') => number;
  addMealToLog: (mealName: string) => void;
  addRecentMeal: (meal: Omit<MealEntry, 'id' | 'timestamp'>) => Promise<void>;
  getRecentMeals: () => MealEntry[];
  clearRecentMeals: () => Promise<void>;
  incrementWater: () => void;
  updateWeight: (newWeight: number) => void;
  getHealthScore: () => number;
  getWeeklyAverage: (metric: string) => number;
  recalculateNutrition: () => void;
  syncNutritionFromTracker: () => Promise<void>;
  forceRefreshNutrition: () => Promise<void>;
  // Add weight progress notification
  onWeightProgressUpdate: (callback: () => void) => () => void; // Returns unsubscribe function
  notifyWeightProgressUpdate: () => void; // Trigger weight progress update
}

// Create context
const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

// Provider component
export const ProfileProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [profile, setProfile] = useState<UserProfile>(getDefaultProfile());
  const [isProfileLoaded, setIsProfileLoaded] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Weight progress update listeners
  const [weightProgressListeners, setWeightProgressListeners] = useState<Set<() => void>>(new Set());

  // Notify weight progress listeners
  const notifyWeightProgressUpdate = useCallback(() => {
    weightProgressListeners.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('❌ Error in weight progress listener:', error);
      }
    });
  }, [weightProgressListeners]);

  // Subscribe to weight progress updates
  const onWeightProgressUpdate = useCallback((callback: () => void) => {
    setWeightProgressListeners(prev => new Set([...prev, callback]));

    // Return unsubscribe function
    return () => {
      setWeightProgressListeners(prev => {
        const newSet = new Set(prev);
        newSet.delete(callback);
        return newSet;
      });
    };
  }, []);
  const [dailyData, setDailyData] = useState<DailyData>({
    date: new Date().toISOString().split('T')[0],
    caloriesConsumed: 0, // Start with zero - real data only
    proteinConsumed: 0,
    waterConsumed: 0,
    stepsCompleted: 0,
    workoutsCompleted: 0,
    mealsLogged: [], // Empty array - no mock meals
    recentMeals: [], // Empty array - will be loaded from database
    weight: 0, // Will be set from profile
    mood: '',
    energyLevel: 0,
    sleepHours: 0,
  });

  const dbService = DatabaseIntegrationService;
  const notificationService = NotificationService;

  // Initialize profile from storage or create new one
  useEffect(() => {
    const initProfile = async () => {
      try {
        console.log('🔄 ProfileContext: Initializing profile...');
        const onboardingComplete = await AsyncStorage.getItem('onboardingComplete');
        console.log('🔍 ProfileContext: Onboarding status:', onboardingComplete);

        if (onboardingComplete === 'true') {
          // User has completed onboarding, load their profile
          console.log('📝 ProfileContext: Loading completed profile...');
          await initializeProfile();
        } else {
          // New user, use default profile and mark as loaded immediately
          console.log('🆕 ProfileContext: Using default profile for new user');
          setProfile(getDefaultProfile());
          setIsProfileLoaded(true);
        }
      } catch (error) {
        console.error('❌ ProfileContext: Error in initProfile:', error);
        // Fallback: use default profile and mark as loaded
        setProfile(getDefaultProfile());
        setIsProfileLoaded(true);
      }
    };
    initProfile();
  }, []);

  const initializeProfile = async (forceRefresh = false) => {
    // Prevent multiple simultaneous initializations with proper mutex
    if (!forceRefresh && (isProfileLoaded || isInitializing)) {
      console.log('⚠️ Profile already loaded or initializing, skipping...');
      return;
    }

    setIsInitializing(true);
    try {
      console.log('🔄 Initializing profile...');

      // Try AsyncStorage first (more reliable), then database
      let userProfile = null;

      // Load from AsyncStorage first with validation
      const savedProfile = await AsyncStorage.getItem('userProfile');
      console.log('💾 AsyncStorage profile result:', savedProfile ? 'Found' : 'Not found');

      if (savedProfile) {
        try {
          const parsedProfile = JSON.parse(savedProfile);

          // Validate parsed profile data
          if (parsedProfile && typeof parsedProfile === 'object' && parsedProfile.name) {
            // Additional validation for critical fields
            if (validateProfileData(parsedProfile)) {
              userProfile = parsedProfile;
              console.log('✅ Successfully loaded and validated profile from AsyncStorage:', userProfile.name || 'No name');
            } else {
              console.error('❌ Invalid profile data in AsyncStorage, will use default');
              // Clear corrupted data
              await AsyncStorage.removeItem('userProfile');
            }
          } else {
            console.error('❌ Malformed profile data in AsyncStorage');
            await AsyncStorage.removeItem('userProfile');
          }
        } catch (parseError) {
          console.error('❌ Error parsing saved profile from AsyncStorage:', parseError);
          // Clear corrupted data
          await AsyncStorage.removeItem('userProfile');
        }
      }

      // If no AsyncStorage profile, try database
      if (!userProfile) {
        try {
          userProfile = await dbService.loadProfileFromDatabase();
          console.log('📊 Database profile result:', userProfile ? 'Found' : 'Not found');

          if (userProfile) {
            // Save to AsyncStorage for faster future access
            await AsyncStorage.setItem('userProfile', JSON.stringify(userProfile));
            console.log('✅ Database profile cached to AsyncStorage');
          }
        } catch (dbError) {
          console.error('❌ Error loading from database:', dbError);
        }
      }

      // If still no profile, use default
      if (!userProfile) {
        userProfile = getDefaultProfile();
        console.log('🆕 Using default profile for new user');

        // Save default profile to AsyncStorage
        await AsyncStorage.setItem('userProfile', JSON.stringify(userProfile));
      }

      console.log('🔧 Setting profile state with:', userProfile.name || 'No name');
      setProfile(userProfile);

      // Try to load additional data from database, but don't let it block profile loading
      try {
        // Load recent meals from database
        const recentMeals = await dbService.getRecentMealsFromDatabase(10);

        // Load today's health data from database
        const todaysHealth = await dbService.loadTodaysHealthData();

        // Calculate today's nutrition from database
        const todaysNutrition = await dbService.calculateTodaysNutrition();

        // Initialize daily data with database values
        setDailyData(prev => ({
          ...prev,
          weight: todaysHealth?.weight || userProfile.weight,
          recentMeals,
          caloriesConsumed: todaysNutrition.calories,
          proteinConsumed: todaysNutrition.protein,
          mealsLogged: recentMeals.slice(0, todaysNutrition.mealCount).map(meal => meal.name),
          waterConsumed: todaysHealth?.waterIntake || 0,
          stepsCompleted: todaysHealth?.steps || 0,
          sleepHours: todaysHealth?.sleepHours || 0,
          energyLevel: todaysHealth?.energyLevel || 0,
          mood: todaysHealth?.mood || ''
        }));

        // Recalculate nutrition from today's meals after loading (using proper async)
        const today = new Date().toISOString().split('T')[0];
        const todaysMeals = recentMeals.filter((meal: MealEntry) => {
          const mealDate = new Date(meal.timestamp).toISOString().split('T')[0];
          return mealDate === today;
        });

        const totalCalories = todaysMeals.reduce((sum: number, meal: MealEntry) => sum + (meal.calories || 0), 0);
        const totalProtein = todaysMeals.reduce((sum: number, meal: MealEntry) => sum + (meal.protein || 0), 0);

        if (totalCalories > 0 || totalProtein > 0) {
          setDailyData(prev => ({
            ...prev,
            caloriesConsumed: totalCalories,
            proteinConsumed: totalProtein,
            mealsLogged: todaysMeals.map((meal: MealEntry) => meal.name)
          }));
        }

        // CRITICAL FIX: Mark profile as loaded BEFORE potentially blocking operations
        setIsProfileLoaded(true);
        console.log('✅ ProfileContext: Profile marked as loaded (before background operations)');

        // Initialize WeeklyPlanManager with the loaded profile (non-blocking)
        try {
          const planInitPromise = WeeklyPlanManager.initializeWeeklyPlanSystem(userProfile);
          await Promise.race([
            planInitPromise,
            new Promise((_, reject) => setTimeout(() => reject(new Error('Plan init timeout')), 5000))
          ]);
          console.log('✅ ProfileContext: WeeklyPlanManager initialized with profile');
        } catch (planError) {
          console.error('❌ WeeklyPlanManager initialization timeout (non-blocking):', planError);
        }

        // Sync nutrition data from WeightGoalTracker with timeout (non-blocking)
        try {
          const syncPromise = syncNutritionFromTracker();
          await Promise.race([
            syncPromise,
            new Promise((_, reject) => setTimeout(() => reject(new Error('Nutrition sync timeout')), 3000))
          ]);
          console.log('✅ ProfileContext: Nutrition synced from WeightGoalTracker');
        } catch (syncError) {
          console.error('❌ Nutrition sync timeout (non-blocking):', syncError);
        }

        // Schedule daily achievement notifications with timeout (non-blocking)
        try {
          const notificationPromise = notificationService.scheduleDailyAchievementNotification();
          await Promise.race([
            notificationPromise,
            new Promise((_, reject) => setTimeout(() => reject(new Error('Notification timeout')), 2000))
          ]);
          console.log('✅ ProfileContext: Daily notifications scheduled');
        } catch (notificationError) {
          console.error('❌ Notification scheduling timeout (non-blocking):', notificationError);
        }
      } catch (dbError) {
        console.error('❌ Database operations failed, but profile still loaded:', dbError);
        // Ensure profile is still marked as loaded even if database operations fail
        setIsProfileLoaded(true);
      }

    } catch (error) {
      console.error('❌ Error initializing profile:', error);
      // Fallback to default profile
      const defaultProfile = getDefaultProfile();
      setProfile(defaultProfile);
      setIsProfileLoaded(true); // Still mark as loaded even with default profile
    } finally {
      setIsInitializing(false);
    }
  };

  // NOTE: App state handling moved to App.tsx to prevent multiple listeners
  // This prevents refresh loops and ensures centralized state management

  // Validate profile data before updates - ENHANCED for weight goal compatibility
  const validateProfileData = (profileData: Partial<UserProfile>): boolean => {
    try {
      // Convert values to numbers to handle string/number type issues
      const age = Number(profileData.age);
      const height = Number(profileData.height);
      const weight = Number(profileData.weight);
      const targetWeight = Number(profileData.targetWeight);
      const caloriesGoal = Number(profileData.caloriesGoal);
      const proteinGoal = Number(profileData.proteinGoal);

      // Basic validation rules with proper number conversion
      if (profileData.age !== undefined && (isNaN(age) || age < 0 || age > 150)) return false;
      if (profileData.height !== undefined && (isNaN(height) || height < 50 || height > 300)) return false;
      if (profileData.weight !== undefined && (isNaN(weight) || weight < 20 || weight > 500)) return false;
      if (profileData.targetWeight !== undefined && (isNaN(targetWeight) || targetWeight < 20 || targetWeight > 500)) return false;
      if (profileData.caloriesGoal !== undefined && (isNaN(caloriesGoal) || caloriesGoal < 800 || caloriesGoal > 5000)) return false;
      if (profileData.proteinGoal !== undefined && (isNaN(proteinGoal) || proteinGoal < 20 || proteinGoal > 400)) return false;

      return true;
    } catch (error) {
      console.error('❌ Profile validation error:', error);
      return false;
    }
  };

  // Update profile function with optimistic updates and rollback
  const updateProfile = async (field: keyof UserProfile, value: any) => {
    // Validate the update
    const testUpdate = { [field]: value };
    if (!validateProfileData(testUpdate)) {
      console.error('❌ Invalid profile data:', field, value);
      return;
    }

    // Store original profile for rollback
    const originalProfile = { ...profile };

    const updatedProfile = {
      ...profile,
      [field]: value
    };

    // Recalculate BMI if height or weight changed
    if (field === 'height' || field === 'weight') {
      const height = updatedProfile.height || profile.height;
      const weight = updatedProfile.weight || profile.weight;
      if (height && weight) {
        const heightInMeters = height / 100;
        const bmi = weight / (heightInMeters * heightInMeters);
        updatedProfile.bmi = Math.round(bmi * 10) / 10;
      }
    }

    // Optimistic update - update UI immediately
    setProfile(updatedProfile);

    // Sync to database and AsyncStorage with proper rollback
    try {
      // Always save to AsyncStorage first (more reliable)
      await AsyncStorage.setItem('userProfile', JSON.stringify(updatedProfile));
      console.log('✅ Profile saved to AsyncStorage:', field, value);

      // Then try to sync to database
      try {
        await dbService.syncProfileToDatabase(updatedProfile);
        console.log('✅ Profile synced to database:', field, value);
      } catch (dbError) {
        console.error('⚠️ Database sync failed, but AsyncStorage saved:', dbError);
        // Database failure is not critical since AsyncStorage succeeded
      }
    } catch (error) {
      console.error('❌ Critical error saving profile:', error);
      // Rollback to original profile if save failed
      setProfile(originalProfile);
      throw error; // Re-throw to let caller know save failed
    }
  };

  // Batch update profile function for onboarding
  const updateProfileBatch = async (updates: Partial<UserProfile>) => {
    console.log('🔄 ProfileContext: Starting batch update with:', Object.keys(updates));

    const updatedProfile = {
      ...profile,
      ...updates,
      updatedAt: new Date().toISOString() // Always update timestamp
    };

    // Recalculate BMI if height or weight changed
    if (updates.height || updates.weight) {
      const height = updates.height || profile.height;
      const weight = updates.weight || profile.weight;
      if (height && weight) {
        const heightInMeters = height / 100;
        const bmi = weight / (heightInMeters * heightInMeters);
        updatedProfile.bmi = Math.round(bmi * 10) / 10;
        console.log('📊 ProfileContext: Recalculated BMI:', updatedProfile.bmi);
      }
    }

    // Update state first
    console.log('📝 ProfileContext: Updating profile state...');
    setProfile(updatedProfile);

    // Save to AsyncStorage and database
    try {
      // Always save to AsyncStorage first (more reliable)
      console.log('💾 ProfileContext: Saving to AsyncStorage...');
      await AsyncStorage.setItem('userProfile', JSON.stringify(updatedProfile));
      console.log('✅ ProfileContext: Profile batch saved to AsyncStorage:', Object.keys(updates));

      // Verify the save worked with detailed logging
      const verification = await AsyncStorage.getItem('userProfile');
      if (verification) {
        const parsed = JSON.parse(verification);
        console.log('✅ ProfileContext: AsyncStorage verification successful');
        console.log('📋 ProfileContext: Verified profile name:', parsed.name || 'No name');
        console.log('📋 ProfileContext: Verified profile complete:', parsed.isProfileComplete);
      } else {
        console.error('❌ ProfileContext: AsyncStorage verification failed - no data found');
        throw new Error('Profile save verification failed');
      }

      // Mark profile as loaded after successful save
      console.log('✅ ProfileContext: Marking profile as loaded');
      setIsProfileLoaded(true);

      // Then try to sync to database (non-blocking)
      try {
        console.log('🔄 ProfileContext: Syncing to database...');
        await dbService.syncProfileToDatabase(updatedProfile);
        console.log('✅ ProfileContext: Profile batch synced to database:', Object.keys(updates));
      } catch (dbError) {
        console.error('⚠️ ProfileContext: Database batch sync failed, but AsyncStorage saved:', dbError);
        // Database failure is not critical since AsyncStorage succeeded
      }
    } catch (error) {
      console.error('❌ ProfileContext: Critical error saving profile batch:', error);
      // Revert the state change if save failed
      setProfile(profile);
      setIsProfileLoaded(false);
      throw error; // Re-throw to let caller know save failed
    }
  };

  // Update daily data function
  const updateDailyData = (field: keyof DailyData, value: any) => {
    setDailyData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Refresh profile data - useful after onboarding completion
  const refreshProfile = async () => {
    if (isRefreshing || isInitializing) {
      console.log('⚠️ Profile refresh already in progress, skipping...');
      return;
    }

    setIsRefreshing(true);
    try {
      console.log('🔄 Refreshing profile data...');

      // Add timeout for the entire refresh operation
      const refreshTimeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Profile refresh timeout')), 5000)
      );

      const refreshOperation = async () => {
        // Force re-check of onboarding status and re-initialize
        const onboardingComplete = await AsyncStorage.getItem('onboardingComplete');
        console.log('🔍 Onboarding status during refresh:', onboardingComplete);

        if (onboardingComplete === 'true') {
          // During onboarding completion, force refresh the profile
          console.log('🔄 Force initializing profile after onboarding completion...');
          await initializeProfile(true); // Force refresh
          console.log('✅ Profile refresh completed successfully');
        } else {
          // Still not complete, use default profile
          setProfile(getDefaultProfile());
          setIsProfileLoaded(true);
          console.log('✅ Using default profile (onboarding not complete)');
        }
      };

      // Race the refresh operation against timeout
      await Promise.race([refreshOperation(), refreshTimeout]);
    } catch (error) {
      console.error('❌ Profile refresh failed or timed out:', error);
      // Ensure profile is marked as loaded even if refresh fails
      if (!isProfileLoaded) {
        setProfile(getDefaultProfile());
        setIsProfileLoaded(true);
        console.log('✅ Fallback: Using default profile after refresh failure');
      }
    } finally {
      setIsRefreshing(false);
    }
  };

  // Calculate BMI
  const calculateBMI = (): number => {
    const heightInMeters = profile.height / 100;
    return Math.round((profile.weight / (heightInMeters * heightInMeters)) * 10) / 10;
  };

  // Calculate remaining values
  const calculateCaloriesRemaining = (): number => {
    return Math.max(0, profile.caloriesGoal - dailyData.caloriesConsumed);
  };

  const calculateProteinRemaining = (): number => {
    return Math.max(0, profile.proteinGoal - dailyData.proteinConsumed);
  };

  const calculateWaterRemaining = (): number => {
    return Math.max(0, profile.waterGoal - dailyData.waterConsumed);
  };

  const calculateStepsRemaining = (): number => {
    return Math.max(0, profile.stepsGoal - dailyData.stepsCompleted);
  };

  // Calculate progress percentages
  const getProgressPercentage = (type: 'calories' | 'protein' | 'water' | 'steps'): number => {
    switch (type) {
      case 'calories':
        return Math.min(100, (dailyData.caloriesConsumed / profile.caloriesGoal) * 100);
      case 'protein':
        return Math.min(100, (dailyData.proteinConsumed / profile.proteinGoal) * 100);
      case 'water':
        return Math.min(100, (dailyData.waterConsumed / profile.waterGoal) * 100);
      case 'steps':
        return Math.min(100, (dailyData.stepsCompleted / profile.stepsGoal) * 100);
      default:
        return 0;
    }
  };

  // Add meal to log
  const addMealToLog = (mealName: string) => {
    setDailyData(prev => ({
      ...prev,
      mealsLogged: [...prev.mealsLogged, mealName]
    }));
  };

  // Add recent meal with database sync, nutrition calculation, and image storage
  const addRecentMeal = async (meal: Omit<MealEntry, 'id' | 'timestamp'>) => {
    try {
      const newMeal: MealEntry = {
        ...meal,
        id: Date.now().toString(),
        timestamp: Date.now()
      };

      // If meal has an image URI (from any source), store it in AsyncStorage for persistence
      if (meal.imageUri) {
        try {
          const mealImagesJson = await AsyncStorage.getItem('recent_meal_images');
          const mealImages = mealImagesJson ? JSON.parse(mealImagesJson) : {};

          // Store image URI with meal ID
          mealImages[newMeal.id] = meal.imageUri;

          // Keep only last 20 meal images to prevent storage bloat
          const imageKeys = Object.keys(mealImages);
          if (imageKeys.length > 20) {
            const sortedKeys = imageKeys.sort((a, b) => parseInt(b) - parseInt(a));
            const keysToKeep = sortedKeys.slice(0, 20);
            const trimmedImages: {[key: string]: string} = {};
            keysToKeep.forEach(key => {
              trimmedImages[key] = mealImages[key];
            });
            await AsyncStorage.setItem('recent_meal_images', JSON.stringify(trimmedImages));
          } else {
            await AsyncStorage.setItem('recent_meal_images', JSON.stringify(mealImages));
          }

          console.log(`📸 Stored image for meal: ${meal.name} (source: ${meal.source || 'unknown'})`);
        } catch (imageError) {
          console.error('❌ Error storing meal image:', imageError);
          // Continue without image storage - don't fail the meal logging
        }
      }

      // For weekly plan meals, try to find cached image if no direct imageUri
      if (!meal.imageUri && meal.source === 'weekly_plan') {
        try {
          const cachedImagesJson = await AsyncStorage.getItem('permanent_meal_image_cache');
          if (cachedImagesJson) {
            const cachedImages: {[key: string]: any} = JSON.parse(cachedImagesJson);
            const normalizedMealName = meal.name.toLowerCase().trim();
            const cachedEntry = cachedImages[normalizedMealName];

            if (cachedEntry && cachedEntry.imageUrl) {
              // Store the cached image URL for this meal ID
              const mealImagesJson = await AsyncStorage.getItem('recent_meal_images');
              const mealImages = mealImagesJson ? JSON.parse(mealImagesJson) : {};
              mealImages[newMeal.id] = cachedEntry.imageUrl;
              await AsyncStorage.setItem('recent_meal_images', JSON.stringify(mealImages));
              console.log(`📸 Found and stored cached image for weekly plan meal: ${meal.name}`);
            }
          }
        } catch (cacheError) {
          console.error('❌ Error retrieving cached image for weekly plan meal:', cacheError);
        }
      }

      // Sync to database first
      await dbService.syncMealToDatabase(newMeal);

      // Log to WeightGoalTracker for progress tracking
      const today = new Date().toISOString().split('T')[0];
      await WeightGoalTracker.getInstance().logDailyNutrition(
        today,
        meal.calories,
        meal.protein || 0,
        meal.source || 'manual'
      );
      console.log(`📊 Logged meal to WeightGoalTracker: ${meal.name} (${meal.calories} cal, ${meal.protein || 0}g protein)`);

      // Get updated meals from database to ensure consistency
      const updatedMeals = await dbService.getRecentMealsFromDatabase(10);

      // Calculate today's nutrition from database
      const todaysNutrition = await dbService.calculateTodaysNutrition();

      // Update state with database values
      setDailyData(prev => ({
        ...prev,
        recentMeals: updatedMeals,
        caloriesConsumed: todaysNutrition.calories,
        proteinConsumed: todaysNutrition.protein,
        mealsLogged: [...prev.mealsLogged, meal.name]
      }));

      // Sync nutrition from WeightGoalTracker to ensure consistency
      await syncNutritionFromTracker();

      // Sync nutrition from WeightGoalTracker to ensure consistency
      await syncNutritionFromTracker();

      // Keep AsyncStorage for backward compatibility
      await AsyncStorage.setItem('recentMeals', JSON.stringify(updatedMeals));

      // Check for achievements and send notifications
      await checkAndSendAchievementNotifications(todaysNutrition);

      // Notify weight progress listeners that data has been updated
      notifyWeightProgressUpdate();

      console.log('✅ Meal added and synced to database with image storage:', meal.name);
    } catch (error) {
      console.error('❌ Error adding recent meal:', error);
    }
  };

  // Get recent meals
  const getRecentMeals = (): MealEntry[] => {
    return dailyData.recentMeals.slice(0, 5); // Return only the 5 most recent
  };

  // Clear recent meals
  const clearRecentMeals = async () => {
    try {
      setDailyData(prev => ({
        ...prev,
        recentMeals: []
      }));
      await AsyncStorage.removeItem('recentMeals');
    } catch (error) {
      console.error('Error clearing recent meals:', error);
    }
  };

  // Increment water
  const incrementWater = () => {
    setDailyData(prev => ({
      ...prev,
      waterConsumed: Math.min(prev.waterConsumed + 1, profile.waterGoal + 2)
    }));
  };

  // Update weight
  const updateWeight = (newWeight: number) => {
    updateProfile('weight', newWeight);
    setDailyData(prev => ({
      ...prev,
      weight: newWeight
    }));
  };

  // Calculate health score
  const getHealthScore = (): number => {
    const caloriesScore = Math.min(100, getProgressPercentage('calories'));
    const proteinScore = Math.min(100, getProgressPercentage('protein'));
    const waterScore = Math.min(100, getProgressPercentage('water'));
    const stepsScore = Math.min(100, getProgressPercentage('steps'));
    
    return Math.round((caloriesScore + proteinScore + waterScore + stepsScore) / 4);
  };

  // Check and send achievement notifications
  const checkAndSendAchievementNotifications = async (nutrition: any) => {
    try {
      // Check calorie goal achievement
      if (nutrition.calories >= profile.caloriesGoal && nutrition.calories < profile.caloriesGoal + 100) {
        await notificationService.sendAchievementNotification({
          title: '🎯 Calorie Goal Achieved!',
          message: `Great job! You've reached your daily calorie goal of ${profile.caloriesGoal} calories.`,
          achievementType: 'daily_goals',
          data: { type: 'calories', value: nutrition.calories, goal: profile.caloriesGoal }
        });
      }

      // Check protein goal achievement
      if (nutrition.protein >= profile.proteinGoal && nutrition.protein < profile.proteinGoal + 10) {
        await notificationService.sendAchievementNotification({
          title: '💪 Protein Target Hit!',
          message: `Excellent! You've met your protein goal of ${profile.proteinGoal}g today.`,
          achievementType: 'daily_goals',
          data: { type: 'protein', value: nutrition.protein, goal: profile.proteinGoal }
        });
      }

      // Check meal logging streak
      if (nutrition.mealCount >= 3) {
        await notificationService.sendAchievementNotification({
          title: '🍽️ Meal Logging Champion!',
          message: `You've logged ${nutrition.mealCount} meals today. Keep up the great tracking!`,
          achievementType: 'streak',
          data: { type: 'meals', count: nutrition.mealCount }
        });
      }
    } catch (error) {
      console.error('❌ Error checking achievements:', error);
    }
  };

  // Sync nutrition data from WeightGoalTracker (authoritative source)
  const syncNutritionFromTracker = async () => {
    try {
      const today = new Date().toISOString().split('T')[0];
      const dailyProgress = await WeightGoalTracker.getInstance().getDailyProgress(today);

      console.log('📊 Current dailyData before WeightGoalTracker sync:', {
        caloriesConsumed: dailyData.caloriesConsumed,
        proteinConsumed: dailyData.proteinConsumed,
        mealsLogged: dailyData.mealsLogged.length,
        recentMealsCount: dailyData.recentMeals.length
      });

      if (dailyProgress) {
        console.log('🔄 Syncing nutrition from WeightGoalTracker:', {
          calories: dailyProgress.caloriesConsumed,
          protein: dailyProgress.proteinConsumed,
          fromScanner: dailyProgress.proteinFromScanner,
          fromWeeklyPlan: dailyProgress.proteinFromWeeklyPlan,
          fromManual: dailyProgress.proteinFromManualLog,
          mealsLogged: dailyProgress.mealsLogged
        });

        setDailyData(prev => ({
          ...prev,
          caloriesConsumed: dailyProgress.caloriesConsumed,
          proteinConsumed: dailyProgress.proteinConsumed
        }));

        console.log('✅ Nutrition synced from WeightGoalTracker successfully');
      } else {
        console.log('⚠️ No daily progress found in WeightGoalTracker for today');

        // Fallback: try to calculate from database
        try {
          const todaysNutrition = await dbService.calculateTodaysNutrition();
          console.log('🔄 Fallback: Using database nutrition calculation:', todaysNutrition);

          setDailyData(prev => ({
            ...prev,
            caloriesConsumed: todaysNutrition.calories,
            proteinConsumed: todaysNutrition.protein
          }));
        } catch (fallbackError) {
          console.error('❌ Fallback nutrition calculation failed:', fallbackError);
        }
      }
    } catch (error) {
      console.error('❌ Error syncing nutrition from WeightGoalTracker:', error);
    }
  };

  // Recalculate nutrition from all logged meals for today (fallback method)
  const recalculateNutrition = useCallback(() => {
    const today = new Date().toISOString().split('T')[0];
    const todaysMeals = dailyData.recentMeals.filter(meal => {
      const mealDate = new Date(meal.timestamp).toISOString().split('T')[0];
      return mealDate === today;
    });

    console.log('🔄 Recalculating nutrition from recent meals:', {
      totalMeals: dailyData.recentMeals.length,
      todaysMeals: todaysMeals.length,
      mealsData: todaysMeals.length > 0 ? todaysMeals.map(meal => ({
        name: meal.name,
        calories: meal.calories,
        protein: meal.protein,
        source: meal.source
      })) : []
    });

    const totalCalories = todaysMeals.reduce((sum, meal) => sum + (meal.calories || 0), 0);
    const totalProtein = todaysMeals.reduce((sum, meal) => sum + (meal.protein || 0), 0);

    console.log('📊 Recalculated totals:', {
      totalCalories,
      totalProtein,
      mealNames: todaysMeals.map(meal => meal.name)
    });

    // Only update if values have actually changed
    setDailyData(prev => {
      if (prev.caloriesConsumed !== totalCalories || prev.proteinConsumed !== totalProtein) {
        return {
          ...prev,
          caloriesConsumed: totalCalories,
          proteinConsumed: totalProtein,
          mealsLogged: todaysMeals.map(meal => meal.name)
        };
      }
      return prev; // No change, return same object to prevent re-render
    });

    console.log('✅ Nutrition recalculated from recent meals');
  }, [dailyData.recentMeals]);

  // Get weekly average (improved implementation)
  const getWeeklyAverage = (metric: string): number => {
    // For now, return current day's data since we need synchronous access
    // TODO: Implement proper weekly average calculation with cached data
    switch (metric) {
      case 'calories':
        return dailyData.caloriesConsumed > 0 ? dailyData.caloriesConsumed : 0;
      case 'protein':
        return dailyData.proteinConsumed > 0 ? dailyData.proteinConsumed : 0;
      case 'water':
        return dailyData.waterConsumed > 0 ? dailyData.waterConsumed : 0;
      case 'steps':
        return dailyData.stepsCompleted > 0 ? dailyData.stepsCompleted : 0;
      case 'weight':
        return (dailyData.weight && dailyData.weight > 0) ? dailyData.weight : (profile.weight || 0);
      case 'sleep':
        return (dailyData.sleepHours && dailyData.sleepHours > 0) ? dailyData.sleepHours : 0;
      case 'energy':
        return (dailyData.energyLevel && dailyData.energyLevel > 0) ? dailyData.energyLevel : 0;
      default:
        return 0;
    }
  };

  // Force refresh all nutrition data from all sources (use sparingly)
  const forceRefreshNutrition = useCallback(async () => {
    try {
      console.log('🔄 Force refreshing nutrition data from all sources...');

      // 1. Recalculate from recent meals (this will update state if needed)
      recalculateNutrition();

      // 2. Sync from WeightGoalTracker (this will update state if needed)
      await syncNutritionFromTracker();

      // 3. Recalculate from database
      const todaysNutrition = await dbService.calculateTodaysNutrition();
      console.log('📊 Database nutrition calculation:', todaysNutrition);

      // Use the highest values from all sources to ensure accuracy
      const currentCalories = Math.max(dailyData.caloriesConsumed, todaysNutrition.calories);
      const currentProtein = Math.max(dailyData.proteinConsumed, todaysNutrition.protein);

      // Only update if values have actually changed
      setDailyData(prev => {
        if (prev.caloriesConsumed !== currentCalories || prev.proteinConsumed !== currentProtein) {
          console.log('✅ Force refresh completed - values updated:', {
            finalCalories: currentCalories,
            finalProtein: currentProtein
          });
          return {
            ...prev,
            caloriesConsumed: currentCalories,
            proteinConsumed: currentProtein
          };
        }
        console.log('✅ Force refresh completed - no changes needed');
        return prev; // No change, return same object to prevent re-render
      });
    } catch (error) {
      console.error('❌ Error force refreshing nutrition:', error);
    }
  }, [dailyData.caloriesConsumed, dailyData.proteinConsumed, recalculateNutrition, syncNutritionFromTracker]);

  const contextValue: ProfileContextType = {
    profile,
    dailyData,
    isProfileLoaded,
    updateProfile,
    updateProfileBatch,
    updateDailyData,
    refreshProfile,
    calculateBMI,
    calculateCaloriesRemaining,
    calculateProteinRemaining,
    calculateWaterRemaining,
    calculateStepsRemaining,
    getProgressPercentage,
    addMealToLog,
    addRecentMeal,
    getRecentMeals,
    clearRecentMeals,
    incrementWater,
    updateWeight,
    getHealthScore,
    getWeeklyAverage,
    recalculateNutrition,
    syncNutritionFromTracker,
    forceRefreshNutrition,
    onWeightProgressUpdate,
    notifyWeightProgressUpdate,
  };

  return (
    <ProfileContext.Provider value={contextValue}>
      {children}
    </ProfileContext.Provider>
  );
};

// Custom hook to use profile context
export const useProfile = (): ProfileContextType => {
  const context = useContext(ProfileContext);
  if (!context) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
};

export default ProfileContext;
