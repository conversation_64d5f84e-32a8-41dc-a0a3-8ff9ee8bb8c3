import AsyncStorage from '@react-native-async-storage/async-storage';
import ApiService from './ApiService';
import DatabaseIntegrationService from './DatabaseIntegrationService';
import RecipeCacheService from './RecipeCacheService';

// Permanent image cache interface for storing Unsplash images (NEVER expires)
interface PermanentImageCache {
  [mealName: string]: {
    imageUrl: string;
    cachedAt: number;
    // No expiration - images are cached permanently
  };
}

export interface MealData {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  imageUrl: string;
  description?: string;
}

export interface WeekPlan {
  week: Array<{
    day: string;
    meals: { [key: string]: MealData };
  }>;
  weekNumber: number;
  year: number;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  isActive: boolean;
  generatedAt: number; // timestamp
}

export interface WeeklyPlanConfig {
  autoGenerate: boolean;
  generateDaysAhead: number; // How many days before week ends to generate next week
  maxStoredWeeks: number; // Maximum number of weeks to keep in storage
}

// Centralized storage keys configuration to prevent inconsistencies
export const STORAGE_KEYS = {
  // Weekly Plan Storage
  WEEKLY_PLANS: 'weekly_plans_storage_v2',
  CURRENT_WEEK: 'current_week_info_v2',
  PLAN_CONFIG: 'weekly_plan_config_v2',
  PLAN_BACKUP: 'weekly_plans_backup_v2',
  PLAN_STATE: 'weekly_plan_state_v2',

  // Legacy keys for migration
  LEGACY_WEEKLY_PLAN: 'weeklyMealPlan',
  LEGACY_CUSTOMIZATIONS: 'weeklyPlanCustomizations',
  LEGACY_PLANS_STORAGE: 'weekly_plans_storage',
  LEGACY_WEEK_INFO: 'current_week_info',
  LEGACY_CONFIG: 'weekly_plan_config',

  // Image Cache (separate from plans)
  IMAGE_CACHE: 'permanent_meal_image_cache_v2',

  // Recovery and validation
  RECOVERY_LOG: 'plan_recovery_log_v2',
  INTEGRITY_CHECK: 'plan_integrity_check_v2'
} as const;

class WeeklyPlanManager {
  private static instance: WeeklyPlanManager;

  // Use centralized storage keys
  private readonly WEEKLY_PLANS_KEY = STORAGE_KEYS.WEEKLY_PLANS;
  private readonly CURRENT_WEEK_KEY = STORAGE_KEYS.CURRENT_WEEK;
  private readonly CONFIG_KEY = STORAGE_KEYS.PLAN_CONFIG;
  private readonly IMAGE_CACHE_KEY = STORAGE_KEYS.IMAGE_CACHE;
  private readonly BACKUP_KEY = STORAGE_KEYS.PLAN_BACKUP;
  private readonly STATE_KEY = STORAGE_KEYS.PLAN_STATE;
  private readonly RECOVERY_LOG_KEY = STORAGE_KEYS.RECOVERY_LOG;
  private readonly INTEGRITY_CHECK_KEY = STORAGE_KEYS.INTEGRITY_CHECK;

  static getInstance(): WeeklyPlanManager {
    if (!WeeklyPlanManager.instance) {
      WeeklyPlanManager.instance = new WeeklyPlanManager();
    }
    return WeeklyPlanManager.instance;
  }

  // Advanced plan recovery and migration system
  private async migrateLegacyData(): Promise<boolean> {
    try {
      console.log('🔄 Checking for legacy meal plan data to migrate...');

      let migrationPerformed = false;

      // Check for legacy weekly meal plan
      const legacyPlan = await AsyncStorage.getItem(STORAGE_KEYS.LEGACY_WEEKLY_PLAN);
      const legacyCustomizations = await AsyncStorage.getItem(STORAGE_KEYS.LEGACY_CUSTOMIZATIONS);

      if (legacyPlan) {
        console.log('📦 Found legacy weekly meal plan, migrating...');
        try {
          const parsedPlan = JSON.parse(legacyPlan);
          const parsedCustomizations = legacyCustomizations ? JSON.parse(legacyCustomizations) : {};

          // Convert legacy format to new format
          const migratedPlan = await this.convertLegacyPlanFormat(parsedPlan, parsedCustomizations);
          if (migratedPlan) {
            await this.storeWeeklyPlan(migratedPlan);
            console.log('✅ Legacy plan migrated successfully');
            migrationPerformed = true;
          }
        } catch (error) {
          console.error('❌ Error migrating legacy plan:', error);
        }

        // Clean up legacy data after successful migration
        await AsyncStorage.removeItem(STORAGE_KEYS.LEGACY_WEEKLY_PLAN);
        await AsyncStorage.removeItem(STORAGE_KEYS.LEGACY_CUSTOMIZATIONS);
      }

      // Check for legacy storage format
      const legacyPlansStorage = await AsyncStorage.getItem(STORAGE_KEYS.LEGACY_PLANS_STORAGE);
      if (legacyPlansStorage && !await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY)) {
        console.log('📦 Found legacy plans storage, migrating...');
        try {
          const legacyPlans = JSON.parse(legacyPlansStorage);
          if (Array.isArray(legacyPlans) && legacyPlans.length > 0) {
            // Migrate to new storage format
            await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, legacyPlansStorage);
            console.log('✅ Legacy plans storage migrated successfully');
            migrationPerformed = true;
          }
        } catch (error) {
          console.error('❌ Error migrating legacy plans storage:', error);
        }

        // Clean up legacy storage
        await AsyncStorage.removeItem(STORAGE_KEYS.LEGACY_PLANS_STORAGE);
      }

      // Migrate other legacy keys
      const legacyWeekInfo = await AsyncStorage.getItem(STORAGE_KEYS.LEGACY_WEEK_INFO);
      if (legacyWeekInfo && !await AsyncStorage.getItem(this.CURRENT_WEEK_KEY)) {
        await AsyncStorage.setItem(this.CURRENT_WEEK_KEY, legacyWeekInfo);
        await AsyncStorage.removeItem(STORAGE_KEYS.LEGACY_WEEK_INFO);
        migrationPerformed = true;
      }

      const legacyConfig = await AsyncStorage.getItem(STORAGE_KEYS.LEGACY_CONFIG);
      if (legacyConfig && !await AsyncStorage.getItem(this.CONFIG_KEY)) {
        await AsyncStorage.setItem(this.CONFIG_KEY, legacyConfig);
        await AsyncStorage.removeItem(STORAGE_KEYS.LEGACY_CONFIG);
        migrationPerformed = true;
      }

      if (migrationPerformed) {
        console.log('✅ Legacy data migration completed successfully');
        await this.logRecoveryAction('MIGRATION', 'Legacy data migrated to new format');
      }

      return migrationPerformed;
    } catch (error) {
      console.error('❌ Error during legacy data migration:', error);
      await this.logRecoveryAction('MIGRATION_ERROR', `Migration failed: ${error.message}`);
      return false;
    }
  }

  // Convert legacy plan format to new format
  private async convertLegacyPlanFormat(legacyPlan: any, customizations: any = {}): Promise<WeekPlan | null> {
    try {
      // If it's already in the new format, return as-is
      if (legacyPlan.week && legacyPlan.weekNumber && legacyPlan.year) {
        return legacyPlan as WeekPlan;
      }

      // Convert old format to new format
      const currentWeekInfo = this.getCurrentWeekInfo();

      const convertedPlan: WeekPlan = {
        week: legacyPlan.week || legacyPlan,
        weekNumber: currentWeekInfo.weekNumber,
        year: currentWeekInfo.year,
        startDate: currentWeekInfo.startDate.toISOString(),
        endDate: currentWeekInfo.endDate.toISOString(),
        isActive: true,
        generatedAt: Date.now()
      };

      return convertedPlan;
    } catch (error) {
      console.error('❌ Error converting legacy plan format:', error);
      return null;
    }
  }

  // Advanced plan recovery system with multiple strategies
  private async attemptPlanRecovery(corruptedData: string, errorType: string): Promise<WeekPlan | null> {
    console.log(`🔧 Attempting plan recovery for error type: ${errorType}`);

    const recoveryStrategies = [
      () => this.recoverFromBackup(),
      () => this.repairCorruptedJSON(corruptedData),
      () => this.reconstructFromPartialData(corruptedData),
      () => this.recoverFromDatabase(),
      () => this.recoverFromIntegrityCheck()
    ];

    for (let i = 0; i < recoveryStrategies.length; i++) {
      try {
        console.log(`🔧 Trying recovery strategy ${i + 1}/${recoveryStrategies.length}`);
        const recoveredPlan = await recoveryStrategies[i]();

        if (recoveredPlan && this.validatePlanStructure(recoveredPlan)) {
          console.log(`✅ Plan recovered using strategy ${i + 1}`);
          await this.logRecoveryAction('RECOVERY_SUCCESS', `Strategy ${i + 1} successful`);

          // Create backup of recovered plan
          await this.createPlanBackup(recoveredPlan);
          return recoveredPlan;
        }
      } catch (strategyError) {
        console.warn(`⚠️ Recovery strategy ${i + 1} failed:`, strategyError);
      }
    }

    console.error('❌ All recovery strategies failed');
    await this.logRecoveryAction('RECOVERY_FAILED', 'All recovery strategies exhausted');
    return null;
  }

  // Recovery strategy 1: Restore from backup
  private async recoverFromBackup(): Promise<WeekPlan | null> {
    try {
      const backupData = await AsyncStorage.getItem(this.BACKUP_KEY);
      if (!backupData) return null;

      const backup = JSON.parse(backupData);
      if (backup.plans && Array.isArray(backup.plans) && backup.plans.length > 0) {
        // Return the most recent active plan from backup
        const activePlan = backup.plans.find((plan: WeekPlan) => plan.isActive);
        return activePlan || backup.plans[0];
      }

      return null;
    } catch (error) {
      console.error('❌ Backup recovery failed:', error);
      return null;
    }
  }

  // Recovery strategy 2: Repair corrupted JSON
  private async repairCorruptedJSON(corruptedData: string): Promise<WeekPlan | null> {
    try {
      // Common JSON corruption fixes
      let repairedData = corruptedData
        .replace(/,\s*}/g, '}')  // Remove trailing commas
        .replace(/,\s*]/g, ']')  // Remove trailing commas in arrays
        .replace(/'/g, '"')      // Replace single quotes with double quotes
        .replace(/(\w+):/g, '"$1":')  // Quote unquoted keys
        .trim();

      // Try to parse repaired data
      const parsed = JSON.parse(repairedData);

      if (Array.isArray(parsed)) {
        const activePlan = parsed.find(plan => plan.isActive);
        return activePlan || parsed[0];
      } else if (parsed.week) {
        return parsed as WeekPlan;
      }

      return null;
    } catch (error) {
      console.error('❌ JSON repair failed:', error);
      return null;
    }
  }

  // Recovery strategy 3: Reconstruct from partial data
  private async reconstructFromPartialData(partialData: string): Promise<WeekPlan | null> {
    try {
      // Try to extract any usable meal data from corrupted string
      const mealPatterns = [
        /"name":\s*"([^"]+)"/g,
        /"calories":\s*(\d+)/g,
        /"day":\s*"([^"]+)"/g
      ];

      const extractedData: any = {};

      for (const pattern of mealPatterns) {
        const matches = [...partialData.matchAll(pattern)];
        if (matches.length > 0) {
          extractedData.hasValidData = true;
          break;
        }
      }

      if (!extractedData.hasValidData) return null;

      // If we found some meal data, create a minimal plan structure
      const currentWeekInfo = this.getCurrentWeekInfo();
      const reconstructedPlan: WeekPlan = {
        week: this.generateMinimalWeekStructure(),
        weekNumber: currentWeekInfo.weekNumber,
        year: currentWeekInfo.year,
        startDate: currentWeekInfo.startDate.toISOString(),
        endDate: currentWeekInfo.endDate.toISOString(),
        isActive: true,
        generatedAt: Date.now()
      };

      return reconstructedPlan;
    } catch (error) {
      console.error('❌ Partial data reconstruction failed:', error);
      return null;
    }
  }

  // Recovery strategy 4: Recover from database
  private async recoverFromDatabase(): Promise<WeekPlan | null> {
    try {
      const DatabaseIntegrationService = (await import('./DatabaseIntegrationService')).default;
      const dbPlan = await DatabaseIntegrationService.loadActiveWeeklyPlan();

      if (dbPlan && dbPlan.week) {
        const currentWeekInfo = this.getCurrentWeekInfo();
        return {
          week: dbPlan.week,
          weekNumber: currentWeekInfo.weekNumber,
          year: currentWeekInfo.year,
          startDate: currentWeekInfo.startDate.toISOString(),
          endDate: currentWeekInfo.endDate.toISOString(),
          isActive: true,
          generatedAt: Date.now()
        };
      }

      return null;
    } catch (error) {
      console.error('❌ Database recovery failed:', error);
      return null;
    }
  }

  // Recovery strategy 5: Recover from integrity check
  private async recoverFromIntegrityCheck(): Promise<WeekPlan | null> {
    try {
      const integrityData = await AsyncStorage.getItem(this.INTEGRITY_CHECK_KEY);
      if (!integrityData) return null;

      const integrity = JSON.parse(integrityData);
      if (integrity.lastValidPlan && integrity.timestamp > Date.now() - (24 * 60 * 60 * 1000)) {
        // Use last valid plan if it's less than 24 hours old
        return integrity.lastValidPlan;
      }

      return null;
    } catch (error) {
      console.error('❌ Integrity check recovery failed:', error);
      return null;
    }
  }

  // Enhanced plan structure validation with auto-repair
  private validatePlanStructure(plan: any, autoRepair: boolean = true): plan is WeekPlan {
    if (!plan || typeof plan !== 'object') return false;

    const issues: string[] = [];
    let repaired = false;

    // Check and repair basic structure
    if (!Array.isArray(plan.week)) {
      if (autoRepair) {
        plan.week = this.generateMinimalWeekStructure();
        issues.push('Repaired missing week array');
        repaired = true;
      } else {
        return false;
      }
    }

    if (typeof plan.weekNumber !== 'number') {
      if (autoRepair) {
        const currentWeekInfo = this.getCurrentWeekInfo();
        plan.weekNumber = currentWeekInfo.weekNumber;
        issues.push('Repaired missing weekNumber');
        repaired = true;
      } else {
        return false;
      }
    }

    if (typeof plan.year !== 'number') {
      if (autoRepair) {
        const currentWeekInfo = this.getCurrentWeekInfo();
        plan.year = currentWeekInfo.year;
        issues.push('Repaired missing year');
        repaired = true;
      } else {
        return false;
      }
    }

    if (typeof plan.generatedAt !== 'number') {
      if (autoRepair) {
        plan.generatedAt = Date.now();
        issues.push('Repaired missing generatedAt');
        repaired = true;
      } else {
        return false;
      }
    }

    if (typeof plan.isActive !== 'boolean') {
      if (autoRepair) {
        plan.isActive = true;
        issues.push('Repaired missing isActive');
        repaired = true;
      } else {
        return false;
      }
    }

    if (!plan.startDate || !plan.endDate) {
      if (autoRepair) {
        const currentWeekInfo = this.getCurrentWeekInfo();
        plan.startDate = currentWeekInfo.startDate.toISOString();
        plan.endDate = currentWeekInfo.endDate.toISOString();
        issues.push('Repaired missing date range');
        repaired = true;
      } else {
        return false;
      }
    }

    // Validate week structure
    if (plan.week.length !== 7) {
      if (autoRepair) {
        plan.week = this.repairWeekStructure(plan.week);
        issues.push('Repaired week structure');
        repaired = true;
      } else {
        return false;
      }
    }

    if (repaired) {
      console.log(`🔧 Plan structure auto-repaired: ${issues.join(', ')}`);
      this.logRecoveryAction('AUTO_REPAIR', issues.join(', '));
    }

    return true;
  }

  // Generate minimal week structure for recovery
  private generateMinimalWeekStructure(): Array<{ day: string; meals: { [key: string]: any } }> {
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    const mealTypes = ['breakfast', 'lunch', 'dinner'];

    return days.map(day => ({
      day,
      meals: mealTypes.reduce((meals, type) => {
        meals[type] = {
          name: `Recovery ${type.charAt(0).toUpperCase() + type.slice(1)}`,
          calories: 400,
          protein: 20,
          carbs: 40,
          fat: 15,
          ingredients: ['Recovery meal - please regenerate plan'],
          instructions: ['This is a recovery meal. Please generate a new plan.'],
          imageUrl: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=600&h=300&fit=crop&crop=center&q=80'
        };
        return meals;
      }, {} as { [key: string]: any })
    }));
  }

  // Repair week structure
  private repairWeekStructure(existingWeek: any[]): Array<{ day: string; meals: { [key: string]: any } }> {
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    const repairedWeek = this.generateMinimalWeekStructure();

    // Try to preserve existing valid data
    if (Array.isArray(existingWeek)) {
      existingWeek.forEach((dayData, index) => {
        if (dayData && dayData.day && dayData.meals && index < 7) {
          const dayIndex = days.indexOf(dayData.day);
          if (dayIndex !== -1) {
            repairedWeek[dayIndex] = {
              ...repairedWeek[dayIndex],
              ...dayData,
              day: days[dayIndex] // Ensure correct day name
            };
          }
        }
      });
    }

    return repairedWeek;
  }

  // Multi-layer backup system
  private async createPlanBackup(plan: WeekPlan): Promise<void> {
    try {
      const backup = {
        timestamp: Date.now(),
        plans: [plan],
        version: '2.0',
        checksum: this.generateChecksum(plan)
      };

      await AsyncStorage.setItem(this.BACKUP_KEY, JSON.stringify(backup));

      // Also create integrity checkpoint
      await this.createIntegrityCheckpoint(plan);

      console.log('✅ Plan backup created successfully');
    } catch (error) {
      console.error('❌ Error creating plan backup:', error);
    }
  }

  // Create integrity checkpoint
  private async createIntegrityCheckpoint(plan: WeekPlan): Promise<void> {
    try {
      const checkpoint = {
        timestamp: Date.now(),
        lastValidPlan: plan,
        checksum: this.generateChecksum(plan),
        weekInfo: this.getCurrentWeekInfo()
      };

      await AsyncStorage.setItem(this.INTEGRITY_CHECK_KEY, JSON.stringify(checkpoint));
    } catch (error) {
      console.error('❌ Error creating integrity checkpoint:', error);
    }
  }

  // Generate checksum for plan validation
  private generateChecksum(plan: WeekPlan): string {
    try {
      const planString = JSON.stringify({
        weekNumber: plan.weekNumber,
        year: plan.year,
        weekLength: plan.week.length,
        mealCount: plan.week.reduce((count, day) => count + Object.keys(day.meals).length, 0)
      });

      // Simple checksum algorithm
      let hash = 0;
      for (let i = 0; i < planString.length; i++) {
        const char = planString.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
      }

      return hash.toString(16);
    } catch (error) {
      return 'invalid';
    }
  }

  // Recovery action logging
  private async logRecoveryAction(action: string, details: string): Promise<void> {
    try {
      const logEntry = {
        timestamp: Date.now(),
        action,
        details,
        weekInfo: this.getCurrentWeekInfo()
      };

      const existingLog = await AsyncStorage.getItem(this.RECOVERY_LOG_KEY);
      const log = existingLog ? JSON.parse(existingLog) : [];

      log.push(logEntry);

      // Keep only last 50 entries
      if (log.length > 50) {
        log.splice(0, log.length - 50);
      }

      await AsyncStorage.setItem(this.RECOVERY_LOG_KEY, JSON.stringify(log));
    } catch (error) {
      console.error('❌ Error logging recovery action:', error);
    }
  }

  // Get recovery statistics
  async getRecoveryStats(): Promise<{
    totalRecoveries: number;
    lastRecovery: string | null;
    successRate: number;
    commonIssues: string[];
  }> {
    try {
      const logData = await AsyncStorage.getItem(this.RECOVERY_LOG_KEY);
      if (!logData) {
        return { totalRecoveries: 0, lastRecovery: null, successRate: 100, commonIssues: [] };
      }

      const log = JSON.parse(logData);
      const recoveries = log.filter((entry: any) => entry.action.includes('RECOVERY'));
      const successes = log.filter((entry: any) => entry.action === 'RECOVERY_SUCCESS');

      const successRate = recoveries.length > 0 ? (successes.length / recoveries.length) * 100 : 100;

      const lastRecovery = recoveries.length > 0
        ? new Date(recoveries[recoveries.length - 1].timestamp).toLocaleString()
        : null;

      // Analyze common issues
      const issueMap = new Map<string, number>();
      log.forEach((entry: any) => {
        if (entry.action.includes('ERROR') || entry.action.includes('FAILED')) {
          const issue = entry.details.split(':')[0] || entry.action;
          issueMap.set(issue, (issueMap.get(issue) || 0) + 1);
        }
      });

      const commonIssues = Array.from(issueMap.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([issue]) => issue);

      return {
        totalRecoveries: recoveries.length,
        lastRecovery,
        successRate: Math.round(successRate),
        commonIssues
      };
    } catch (error) {
      console.error('❌ Error getting recovery stats:', error);
      return { totalRecoveries: 0, lastRecovery: null, successRate: 0, commonIssues: [] };
    }
  }

  // Get current week information using proper ISO week calculation
  getCurrentWeekInfo(): { weekNumber: number; year: number; startDate: Date; endDate: Date } {
    const now = new Date();

    // Calculate start of current week (Monday) - ISO week starts on Monday
    const dayOfWeek = now.getDay();
    const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
    const startDate = new Date(now);
    startDate.setDate(now.getDate() + daysToMonday);
    startDate.setHours(0, 0, 0, 0);

    // Calculate end of current week (Sunday)
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 6);
    endDate.setHours(23, 59, 59, 999);

    // Proper ISO week number calculation
    const weekNumber = this.getISOWeekNumber(startDate);
    const weekYear = this.getISOWeekYear(startDate);

    console.log(`📅 Week calculation: Week ${weekNumber} of ${weekYear}`);
    console.log(`📅 Week period: ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`);

    return {
      weekNumber,
      year: weekYear,
      startDate,
      endDate
    };
  }

  // Calculate ISO week number (1-53) - handles year boundaries correctly
  private getISOWeekNumber(date: Date): number {
    const tempDate = new Date(date.getTime());
    const dayOfWeek = (tempDate.getDay() + 6) % 7; // Monday = 0, Sunday = 6
    tempDate.setDate(tempDate.getDate() - dayOfWeek + 3); // Thursday of this week
    const firstThursday = tempDate.getTime();
    tempDate.setMonth(0, 1); // January 1st
    if (tempDate.getDay() !== 4) {
      tempDate.setMonth(0, 1 + ((4 - tempDate.getDay()) + 7) % 7);
    }
    return 1 + Math.ceil((firstThursday - tempDate.getTime()) / 604800000); // 604800000 = 7 * 24 * 3600 * 1000
  }

  // Get the year for ISO week (can differ from calendar year)
  private getISOWeekYear(date: Date): number {
    const tempDate = new Date(date.getTime());
    const dayOfWeek = (tempDate.getDay() + 6) % 7;
    tempDate.setDate(tempDate.getDate() - dayOfWeek + 3); // Thursday of this week
    return tempDate.getFullYear();
  }

  // FIXED: Enhance meal plan with BASIC data only (no detailed recipe generation)
  private async enhanceMealPlanWithData(basicWeek: Array<{day: string; meals: {[key: string]: string}}>): Promise<Array<{day: string; meals: {[key: string]: MealData}}>> {
    console.log('🍽️ OPTIMIZED: Enhancing meal plan with BASIC meal data only (no recipe details)...');
    const startTime = Date.now();

    const enhancedWeek = [];
    const allMealNames: string[] = [];

    // First pass: Generate meal data and collect meal names
    for (const day of basicWeek) {
      console.log(`📅 Processing ${day.day} with AI-generated content...`);

      const enhancedDay = {
        day: day.day,
        meals: {} as {[key: string]: MealData}
      };

      // Collect all meal names for image preloading
      Object.values(day.meals).forEach(mealName => {
        if (!allMealNames.includes(mealName)) {
          allMealNames.push(mealName);
        }
      });

      // FIXED: Process meals with BASIC data only - NO recipe generation
      for (const [mealType, mealName] of Object.entries(day.meals)) {
        try {
          console.log(`🍽️ Generating BASIC data for: ${mealName} (${mealType}) - NO recipe details`);
          // Generate BASIC meal data only (calories, nutrition, image) - NO recipe API calls
          enhancedDay.meals[mealType] = await this.generateBasicMealData(mealName, mealType);
        } catch (error) {
          console.error(`❌ Error generating basic data for ${mealName}:`, error);
          // Use estimated data as fallback
          enhancedDay.meals[mealType] = await this.getEstimatedMealData(mealName, mealType);
        }
      }

      enhancedWeek.push(enhancedDay);
    }

    // Second pass: Preload ALL images in parallel BEFORE completing
    console.log(`🖼️ Preloading images for ${allMealNames.length} unique meals...`);
    await this.preloadAllMealImages(allMealNames);

    const endTime = Date.now();
    console.log(`✅ OPTIMIZED: Enhanced meal plan with BASIC data and preloaded images in ${endTime - startTime}ms`);
    console.log(`🚀 PERFORMANCE: Avoided ${allMealNames.length} recipe API calls during plan generation`);
    return enhancedWeek;
  }

  // Generate sophisticated fallback using RecipeCacheService (still AI-powered!)
  private async generateSophisticatedFallback(mealName: string, mealType: string): Promise<MealData> {
    try {
      console.log(`🔄 Generating sophisticated fallback for: ${mealName}`);

      // Use RecipeCacheService which has sophisticated fallbacks
      const cachedRecipe = await RecipeCacheService.getOrGenerateRecipe(mealName, 'weekly_plan');

      return {
        name: mealName,
        calories: cachedRecipe.nutrition.calories,
        protein: cachedRecipe.nutrition.protein,
        carbs: cachedRecipe.nutrition.carbs,
        fat: cachedRecipe.nutrition.fat,
        imageUrl: cachedRecipe.imageUrl,
        description: cachedRecipe.description
      };
    } catch (error) {
      console.error(`❌ Even sophisticated fallback failed for ${mealName}:`, error);
      // Last resort - but still better than basic static data
      return await this.getFallbackMealData(mealName, mealType);
    }
  }

  // Basic fallback meal data (last resort only)
  private async getFallbackMealData(mealName: string, mealType: string): Promise<MealData> {
    const imageUrl = await this.getFallbackImage(mealName);

    const calories = this.estimateCalories(mealType);
    return {
      name: mealName,
      calories: calories,
      protein: Math.round((calories * 0.25) / 4), // 25% of calories from protein, 4 cal/g
      carbs: Math.round((calories * 0.45) / 4),   // 45% of calories from carbs, 4 cal/g
      fat: Math.round((calories * 0.30) / 9),     // 30% of calories from fat, 9 cal/g
      imageUrl: imageUrl,
      description: `Healthy ${mealName.toLowerCase()}`
    };
  }

  // FIXED: Generate BASIC meal data only (no recipe API calls)
  private async generateBasicMealData(mealName: string, mealType: string): Promise<MealData> {
    console.log(`🍽️ Generating BASIC data for: ${mealName} (${mealType}) - NO recipe API calls`);

    try {
      // Get estimated nutrition data and image without making recipe API calls
      const calories = this.estimateCalories(mealType);
      const imageUrl = await this.getFallbackImage(mealName);

      return {
        name: mealName,
        calories: calories,
        protein: Math.round((calories * 0.25) / 4), // 25% of calories from protein, 4 cal/g
        carbs: Math.round((calories * 0.45) / 4),   // 45% of calories from carbs, 4 cal/g
        fat: Math.round((calories * 0.30) / 9),     // 30% of calories from fat, 9 cal/g
        imageUrl: imageUrl,
        description: `Healthy ${mealName.toLowerCase()}`
      };
    } catch (error) {
      console.warn(`⚠️ Error generating basic data for ${mealName}:`, error);
      // Use estimated data as final fallback
      return this.getEstimatedMealData(mealName, mealType);
    }
  }



  // Generate complete meal data for a single meal using RecipeCacheService (ONLY used for on-demand generation)
  private async generateCompleteMealData(mealName: string, mealType: string): Promise<MealData> {
    try {
      console.log(`🍽️ Generating COMPLETE data for: ${mealName} (${mealType}) - FULL recipe generation`);

      // Use RecipeCacheService to get or generate recipe
      const cachedRecipe = await RecipeCacheService.getOrGenerateRecipe(mealName, 'weekly_plan');

      return {
        name: mealName,
        calories: cachedRecipe.nutrition.calories,
        protein: cachedRecipe.nutrition.protein,
        carbs: cachedRecipe.nutrition.carbs,
        fat: cachedRecipe.nutrition.fat,
        imageUrl: cachedRecipe.imageUrl,
        description: cachedRecipe.description
      };
    } catch (error) {
      console.warn(`⚠️ Failed to generate complete data for ${mealName}, using estimates:`, error);
      return await this.getEstimatedMealData(mealName, mealType);
    }
  }

  // Get estimated meal data with dynamic Unsplash image
  private async getEstimatedMealData(mealName: string, mealType: string): Promise<MealData> {
    const calories = this.estimateCalories(mealType);
    const imageUrl = await this.getFallbackImage(mealName);

    return {
      name: mealName,
      calories: calories,
      protein: Math.round((calories * 0.25) / 4), // 25% of calories from protein, 4 cal/g
      carbs: Math.round((calories * 0.45) / 4),   // 45% of calories from carbs, 4 cal/g
      fat: Math.round((calories * 0.30) / 9),     // 30% of calories from fat, 9 cal/g
      imageUrl: imageUrl,
      description: `Healthy ${mealName.toLowerCase()}`
    };
  }

  // Estimate calories based on meal type
  private estimateCalories(mealType: string): number {
    const calorieMap: {[key: string]: number} = {
      breakfast: 400,
      lunch: 500,
      dinner: 600,
      snack: 200,
      snack1: 150,
      snack2: 150
    };
    return calorieMap[mealType.toLowerCase()] || 400;
  }

  // Get cached image or fetch new one from Unsplash (PERMANENT CACHING - never expires)
  private async getFallbackImage(mealName: string): Promise<string> {
    // First check if we have a permanently cached image for this meal
    const cachedImage = await this.getCachedImage(mealName);
    if (cachedImage) {
      console.log(`📖 Using PERMANENTLY cached image for ${mealName} - NO API CALL NEEDED: ${cachedImage}`);
      return cachedImage;
    }

    // If not cached, fetch from Unsplash and cache it
    const searchTerm = mealName.toLowerCase().replace(/[^a-z0-9\s]/g, '').trim();
    // Enhance search term by adding "food" for better food-related results
    const enhancedSearchTerm = `${searchTerm} food`;
    const accessKey = 'QPySZeLMRd2Rw0BKoNKpXFwrHY0aSVZMwxvTmZaIZEs';
    const unsplashApiUrl = 'https://api.unsplash.com';

    try {
      console.log(`🖼️ WeeklyPlanManager: PARALLEL Unsplash API call for: ${mealName} (enhanced: ${enhancedSearchTerm}) (will be cached PERMANENTLY)`);

      // Create PARALLEL requests for maximum speed
      const specificSearchPromise = fetch(`${unsplashApiUrl}/search/photos?query=${encodeURIComponent(enhancedSearchTerm)}&orientation=landscape&order_by=relevant&per_page=1&client_id=${accessKey}`);
      const genericSearchPromise = fetch(`${unsplashApiUrl}/search/photos?query=food&orientation=landscape&order_by=relevant&per_page=5&client_id=${accessKey}`);

      // Execute BOTH requests in parallel with 2-second timeout
      const [specificResult, genericResult] = await Promise.allSettled([
        Promise.race([specificSearchPromise, new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 2000))]),
        Promise.race([genericSearchPromise, new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 2000))])
      ]);

      // Try specific search first (fastest response)
      if (specificResult.status === 'fulfilled' && (specificResult.value as Response).ok) {
        const data = await (specificResult.value as Response).json();
        if (data.results && data.results.length > 0) {
          const imageUrl = data.results[0].urls.small;
          console.log(`✅ WeeklyPlanManager: FAST specific image found for ${enhancedSearchTerm}: ${imageUrl}`);

          // Cache the image for future use
          await this.cacheImage(mealName, imageUrl);
          return imageUrl;
        }
      }

      // Fallback to generic food search (parallel backup)
      if (genericResult.status === 'fulfilled' && (genericResult.value as Response).ok) {
        console.log(`⚠️ WeeklyPlanManager: Using PARALLEL generic food search for ${mealName}`);
        const genericData = await (genericResult.value as Response).json();
        if (genericData.results && genericData.results.length > 0) {
          const imageUrl = genericData.results[0].urls.small;
          console.log(`✅ WeeklyPlanManager: FAST generic food image for ${mealName}: ${imageUrl}`);

          // Cache the generic image
          await this.cacheImage(mealName, imageUrl);
          return imageUrl;
        }
      }

      // Both parallel requests failed
      console.log(`⚠️ WeeklyPlanManager: Both parallel requests failed for ${mealName}, using static fallback`);
      throw new Error('Both parallel requests failed');

    } catch (error) {
      console.warn(`⚠️ WeeklyPlanManager: Failed to fetch Unsplash image for ${enhancedSearchTerm} (original: ${mealName}):`, error);

      // Return optimized static fallback image (400px width for better performance)
      const fallbackImage = `https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=400&h=300&fit=crop&crop=center&q=80&auto=format&cs=tinysrgb&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D`;

      // Cache the fallback image too
      await this.cacheImage(mealName, fallbackImage);
      return fallbackImage;
    }
  }

  // Get permanently cached image for a meal (NEVER expires)
  private async getCachedImage(mealName: string): Promise<string | null> {
    try {
      const cacheJson = await AsyncStorage.getItem(this.IMAGE_CACHE_KEY);
      if (!cacheJson) return null;

      const cache: PermanentImageCache = JSON.parse(cacheJson);
      const normalizedMealName = mealName.toLowerCase().trim();
      const cachedEntry = cache[normalizedMealName];

      if (!cachedEntry) return null;

      // Images NEVER expire - return cached image immediately
      console.log(`📖 Found permanently cached image for ${mealName} (cached ${new Date(cachedEntry.cachedAt).toLocaleDateString()})`);
      return cachedEntry.imageUrl;
    } catch (error) {
      console.error('❌ Error getting cached image:', error);
      return null;
    }
  }

  // Cache an image for a meal PERMANENTLY (NEVER expires)
  private async cacheImage(mealName: string, imageUrl: string): Promise<void> {
    try {
      const cacheJson = await AsyncStorage.getItem(this.IMAGE_CACHE_KEY);
      const cache: PermanentImageCache = cacheJson ? JSON.parse(cacheJson) : {};

      const normalizedMealName = mealName.toLowerCase().trim();
      const now = Date.now();

      // Only cache if not already cached (avoid overwriting existing entries)
      if (!cache[normalizedMealName]) {
        cache[normalizedMealName] = {
          imageUrl,
          cachedAt: now
          // No expiration - cached permanently
        };

        await AsyncStorage.setItem(this.IMAGE_CACHE_KEY, JSON.stringify(cache));
        console.log(`💾 PERMANENTLY cached image for ${mealName} - will NEVER make API call again for this meal`);
      } else {
        console.log(`📖 Image already permanently cached for ${mealName} - skipping cache update`);
      }
    } catch (error) {
      console.error('❌ Error caching image:', error);
    }
  }

  // Preload ALL meal images in parallel for instant display
  private async preloadAllMealImages(mealNames: string[]): Promise<void> {
    const startTime = Date.now();
    console.log(`🚀 Starting parallel image preloading for ${mealNames.length} meals...`);

    try {
      // Create promises for all image fetches
      const imagePromises = mealNames.map(async (mealName, index) => {
        try {
          console.log(`🖼️ [${index + 1}/${mealNames.length}] Preloading image for: ${mealName}`);

          // Check if already cached first
          const cachedImage = await this.getCachedImage(mealName);
          if (cachedImage) {
            console.log(`📖 [${index + 1}/${mealNames.length}] Image already cached for: ${mealName}`);
            return { mealName, imageUrl: cachedImage, fromCache: true };
          }

          // Fetch new image if not cached
          const imageUrl = await this.getFallbackImage(mealName);
          console.log(`✅ [${index + 1}/${mealNames.length}] Preloaded image for: ${mealName}`);
          return { mealName, imageUrl, fromCache: false };
        } catch (error) {
          console.error(`❌ [${index + 1}/${mealNames.length}] Failed to preload image for ${mealName}:`, error);
          return { mealName, imageUrl: null, fromCache: false, error: true };
        }
      });

      // Wait for all images to be fetched/cached
      const results = await Promise.all(imagePromises);

      // Log results
      const cached = results.filter(r => r.fromCache).length;
      const fetched = results.filter(r => !r.fromCache && !r.error).length;
      const failed = results.filter(r => r.error).length;

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      console.log(`🎉 Image preloading completed in ${totalTime}ms:`);
      console.log(`   📖 Cached: ${cached} images`);
      console.log(`   🆕 Fetched: ${fetched} images`);
      console.log(`   ❌ Failed: ${failed} images`);
      console.log(`   ⚡ Average: ${(totalTime / mealNames.length).toFixed(1)}ms per image`);

      if (failed > 0) {
        console.warn(`⚠️ ${failed} images failed to preload, but meal plan will still work with fallbacks`);
      }

    } catch (error) {
      console.error('❌ Critical error during image preloading:', error);
      // Don't throw - meal plan should still work without images
    }
  }

  // Get cache statistics (for debugging) - no expiration cleanup needed
  async getImageCacheStats(): Promise<{ totalImages: number; cacheSize: string; oldestCacheDate: string }> {
    try {
      const cacheJson = await AsyncStorage.getItem(this.IMAGE_CACHE_KEY);
      if (!cacheJson) return { totalImages: 0, cacheSize: '0 KB', oldestCacheDate: 'N/A' };

      const cache: PermanentImageCache = JSON.parse(cacheJson);
      const totalImages = Object.keys(cache).length;
      const cacheSize = `${Math.round(cacheJson.length / 1024)} KB`;

      // Find oldest cached image
      const oldestEntry = Object.values(cache).reduce((oldest, current) =>
        current.cachedAt < oldest.cachedAt ? current : oldest
      );
      const oldestCacheDate = oldestEntry ? new Date(oldestEntry.cachedAt).toLocaleDateString() : 'N/A';

      return { totalImages, cacheSize, oldestCacheDate };
    } catch (error) {
      console.error('❌ Error getting cache stats:', error);
      return { totalImages: 0, cacheSize: '0 KB', oldestCacheDate: 'N/A' };
    }
  }

  // Clear ALL cached images (only if user explicitly wants to reset)
  async clearAllImageCache(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.IMAGE_CACHE_KEY);
      console.log(`🧹 Cleared ALL permanently cached images - API calls will be made again for all meals`);
    } catch (error) {
      console.error('❌ Error clearing all image cache:', error);
    }
  }

  // Check if an image is already permanently cached (for debugging/optimization)
  async isImageCached(mealName: string): Promise<boolean> {
    try {
      const cacheJson = await AsyncStorage.getItem(this.IMAGE_CACHE_KEY);
      if (!cacheJson) return false;

      const cache: PermanentImageCache = JSON.parse(cacheJson);
      const normalizedMealName = mealName.toLowerCase().trim();
      return !!cache[normalizedMealName];
    } catch (error) {
      console.error('❌ Error checking if image is cached:', error);
      return false;
    }
  }

  // Replace a specific meal in the current week plan (for alternatives)
  async replaceMealInCurrentPlan(dayName: string, mealType: string, userProfile: any): Promise<MealData | null> {
    try {
      console.log(`🔄 Generating alternative meal for ${dayName} ${mealType}`);

      // Get current plan
      const currentPlan = await this.getActiveWeeklyPlan();
      if (!currentPlan) {
        throw new Error('No active plan found');
      }

      // Find the current meal to get context for better alternatives
      const dayIndex = currentPlan.week.findIndex(day => day.day.toLowerCase() === dayName.toLowerCase());
      if (dayIndex === -1) {
        throw new Error(`Day ${dayName} not found in current plan`);
      }

      const currentMeal = currentPlan.week[dayIndex].meals[mealType];
      if (!currentMeal) {
        throw new Error(`Meal type ${mealType} not found for ${dayName}`);
      }

      // Build goal for alternative meal generation
      const goalComponents = [];
      goalComponents.push(`Generate a different ${mealType} meal to replace "${currentMeal.name}"`);

      if (userProfile.dietaryRestrictions?.length > 0) {
        goalComponents.push(`Dietary restrictions: ${userProfile.dietaryRestrictions.join(', ')}`);
      }
      if (userProfile.allergies?.length > 0) {
        goalComponents.push(`Avoid allergens: ${userProfile.allergies.join(', ')}`);
      }
      if (userProfile.preferredCuisines?.length > 0) {
        goalComponents.push(`Preferred cuisines: ${userProfile.preferredCuisines.join(', ')}`);
      }

      const goal = goalComponents.join('. ');

      // Generate new meal name
      const newMealName = await ApiService.generateSingleMeal(goal);

      // Generate complete meal data using RecipeCacheService
      const newMealData = await this.generateCompleteMealData(newMealName, mealType);

      // Update ONLY the specific meal in the current plan
      currentPlan.week[dayIndex].meals[mealType] = newMealData;

      // Use targeted update method instead of full plan storage
      await this.updateSpecificMealInStorage(currentPlan, dayIndex, mealType, newMealData);

      console.log(`✅ Replaced ${dayName} ${mealType}: "${currentMeal.name}" → "${newMealName}"`);
      return newMealData;

    } catch (error) {
      console.error('❌ Error replacing meal:', error);
      return null;
    }
  }

  // Targeted method to update only a specific meal without affecting the rest of the plan
  private async updateSpecificMealInStorage(plan: WeekPlan, dayIndex: number, mealType: string, newMealData: MealData): Promise<void> {
    try {
      // Get existing plans from storage
      const existingPlansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      const existingPlans: WeekPlan[] = existingPlansJson ? JSON.parse(existingPlansJson) : [];

      // Find and update the current active plan
      const activePlanIndex = existingPlans.findIndex(p =>
        p.isActive &&
        p.weekNumber === plan.weekNumber &&
        p.year === plan.year
      );

      if (activePlanIndex !== -1) {
        // Update only the specific meal
        existingPlans[activePlanIndex].week[dayIndex].meals[mealType] = newMealData;

        // Save back to storage - SINGLE ATOMIC OPERATION
        await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(existingPlans));

        // REMOVED: updateCurrentWeekInfo - unnecessary operation causing re-renders
        // The current week info doesn't need updating when just replacing a meal

        console.log(`✅ Updated specific meal in storage: ${plan.week[dayIndex].day} ${mealType}`);
      } else {
        console.warn('⚠️ Active plan not found in storage, falling back to full plan update');
        await this.saveWeekPlan(plan); // Use saveWeekPlan instead of storeWeeklyPlan
      }

    } catch (error) {
      console.error('❌ Error updating specific meal in storage:', error);
      // Fallback to full plan update
      await this.saveWeekPlan(plan); // Use saveWeekPlan instead of storeWeeklyPlan
    }
  }

  // Enhanced plan validity check with graceful degradation
  async isCurrentPlanValid(): Promise<boolean> {
    try {
      const currentWeekInfo = this.getCurrentWeekInfo();
      const storedWeekInfo = await AsyncStorage.getItem(this.CURRENT_WEEK_KEY);

      if (!storedWeekInfo) {
        console.log('📅 No stored week info found, checking for valid plans...');

        // Instead of immediately returning false, check if we have any valid plan
        const activePlan = await this.getActiveWeeklyPlan();
        if (activePlan && activePlan.weekNumber === currentWeekInfo.weekNumber && activePlan.year === currentWeekInfo.year) {
          console.log('📅 Found valid current week plan, updating week info');
          await this.updateCurrentWeekInfo(currentWeekInfo);
          return true;
        }

        return false;
      }

      let stored;
      try {
        stored = JSON.parse(storedWeekInfo);
      } catch (parseError) {
        console.warn('⚠️ Corrupted week info detected, attempting repair...');

        // Try to repair week info from current context
        try {
          await this.updateCurrentWeekInfo(currentWeekInfo);
          await this.logRecoveryAction('WEEK_INFO_REPAIR', 'Repaired corrupted week info');

          // Check if we have a valid plan for current week
          const activePlan = await this.getActiveWeeklyPlan();
          if (activePlan && activePlan.weekNumber === currentWeekInfo.weekNumber && activePlan.year === currentWeekInfo.year) {
            console.log('✅ Week info repaired and valid plan found');
            return true;
          }
        } catch (repairError) {
          console.error('❌ Week info repair failed:', repairError);
        }

        // Only clear as last resort
        await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);
        await this.logRecoveryAction('WEEK_INFO_CLEAR', 'Repair failed, cleared corrupted data');
        return false;
      }

      // Validate and repair stored data structure
      if (!stored || typeof stored.weekNumber !== 'number' || typeof stored.year !== 'number') {
        console.warn('⚠️ Invalid week info structure, attempting repair...');

        // Try to repair with current week info
        try {
          await this.updateCurrentWeekInfo(currentWeekInfo);
          await this.logRecoveryAction('WEEK_INFO_STRUCTURE_REPAIR', 'Repaired invalid week info structure');

          // Check for valid plan
          const activePlan = await this.getActiveWeeklyPlan();
          if (activePlan && activePlan.weekNumber === currentWeekInfo.weekNumber && activePlan.year === currentWeekInfo.year) {
            return true;
          }
        } catch (repairError) {
          console.error('❌ Structure repair failed:', repairError);
        }

        await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);
        return false;
      }

      const isValid = stored.weekNumber === currentWeekInfo.weekNumber &&
                     stored.year === currentWeekInfo.year;

      console.log(`📅 Week plan validity check: ${isValid ? 'VALID' : 'EXPIRED'}`);
      console.log(`📅 Current: Week ${currentWeekInfo.weekNumber}, ${currentWeekInfo.year}`);
      console.log(`📅 Stored: Week ${stored.weekNumber}, ${stored.year}`);

      // Enhanced plan verification with recovery
      if (isValid) {
        try {
          const activePlan = await this.getActiveWeeklyPlan();
          if (!activePlan) {
            console.log('📅 Week info valid but no active plan found, checking for recovery...');

            // Try to find any plan for current week (even if not active)
            const plansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
            if (plansJson) {
              const plans = JSON.parse(plansJson);
              const currentWeekPlan = plans.find((plan: WeekPlan) =>
                plan.weekNumber === currentWeekInfo.weekNumber &&
                plan.year === currentWeekInfo.year
              );

              if (currentWeekPlan) {
                console.log('📅 Found inactive plan for current week, activating...');
                currentWeekPlan.isActive = true;

                // Deactivate other plans
                plans.forEach((plan: WeekPlan) => {
                  if (plan !== currentWeekPlan) {
                    plan.isActive = false;
                  }
                });

                await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(plans));
                await this.logRecoveryAction('ACTIVATE_WEEK_PLAN', 'Activated existing plan for current week');
                return true;
              }
            }

            return false;
          }

          if (activePlan.weekNumber !== currentWeekInfo.weekNumber || activePlan.year !== currentWeekInfo.year) {
            console.log('📅 Active plan is for different week, checking for current week plan...');

            // Try to find and activate plan for current week
            const plansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
            if (plansJson) {
              const plans = JSON.parse(plansJson);
              const currentWeekPlan = plans.find((plan: WeekPlan) =>
                plan.weekNumber === currentWeekInfo.weekNumber &&
                plan.year === currentWeekInfo.year
              );

              if (currentWeekPlan) {
                console.log('📅 Found plan for current week, switching activation...');

                // Switch active plan
                plans.forEach((plan: WeekPlan) => {
                  plan.isActive = (plan === currentWeekPlan);
                });

                await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(plans));
                await this.logRecoveryAction('SWITCH_ACTIVE_PLAN', 'Switched to current week plan');
                return true;
              }
            }

            return false;
          }
        } catch (planCheckError) {
          console.error('❌ Error during plan verification:', planCheckError);
          // Don't fail validation just because of plan check error
          return isValid;
        }
      }

      return isValid;
    } catch (error) {
      console.error('❌ Error checking plan validity:', error);

      // Graceful degradation - try to determine validity from available data
      try {
        const currentWeekInfo = this.getCurrentWeekInfo();
        const activePlan = await this.getActiveWeeklyPlan();

        if (activePlan && activePlan.weekNumber === currentWeekInfo.weekNumber && activePlan.year === currentWeekInfo.year) {
          console.log('📅 Validity check failed but found valid current week plan');
          await this.updateCurrentWeekInfo(currentWeekInfo);
          await this.logRecoveryAction('VALIDITY_RECOVERY', 'Recovered from validity check error');
          return true;
        }
      } catch (recoveryError) {
        console.error('❌ Validity recovery failed:', recoveryError);
      }

      // Only clear data if absolutely necessary
      try {
        await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);
        await this.logRecoveryAction('VALIDITY_CLEAR', 'Cleared week info due to critical error');
      } catch (clearError) {
        console.error('❌ Error clearing corrupted week info:', clearError);
      }
      return false;
    }
  }

  // Generate new weekly plan for current week
  async generateWeeklyPlan(userProfile: any): Promise<WeekPlan | null> {
    const startTime = performance.now();
    console.log('🚀 PERFORMANCE: Starting weekly plan generation...');

    try {
      const currentWeekInfo = this.getCurrentWeekInfo();
      console.log(`📅 Generating new weekly plan for Week ${currentWeekInfo.weekNumber}, ${currentWeekInfo.year}`);
      console.log('👤 User profile data:', JSON.stringify({
        dietaryRestrictions: userProfile.dietaryRestrictions,
        caloriesGoal: userProfile.caloriesGoal,
        allergies: userProfile.allergies,
        preferredCuisines: userProfile.preferredCuisines,
        activityLevel: userProfile.activityLevel,
        healthGoals: userProfile.healthGoals
      }, null, 2));

      // Map user profile to API format with proper field names and comprehensive health goals
      const apiOptions = {
        dietaryRestrictions: userProfile.dietaryRestrictions || [],
        calorieGoal: userProfile.caloriesGoal || userProfile.dailyCalorieGoal || 2000, // Handle both field names
        mealsPerDay: 3, // Default to 3 meals per day
        preferences: userProfile.dietaryPreferences || userProfile.foodPreferences || [],
        allergies: userProfile.allergies || [],
        preferredCuisines: userProfile.preferredCuisines || [],
        activityLevel: userProfile.activityLevel || 'Moderate',
        healthGoals: [
          ...(userProfile.healthGoals || []),
          ...(userProfile.fitnessObjectives || []),
          ...(userProfile.healthConditions || []),
          userProfile.weightGoal ? `${userProfile.weightGoal} weight` : ''
        ].filter(Boolean)
      };

      console.log('🔄 Mapped API options with comprehensive health goals:', JSON.stringify(apiOptions, null, 2));

      // Generate meal plan using the corrected API method
      const mealPlanResult = await ApiService.generateWeeklyMealPlan(apiOptions);

      if (!mealPlanResult || !mealPlanResult.week) {
        throw new Error('Failed to generate meal plan from API');
      }

      // Enhance meal plan with complete meal data (nutrition + images)
      const enhancedWeek = await this.enhanceMealPlanWithData(mealPlanResult.week);

      // Validate nutrition data and regenerate if needed
      await this.validateAndFixNutritionData(enhancedWeek);

      // Balance nutrition to meet user's daily goals
      await this.balanceWeeklyNutrition(enhancedWeek, userProfile);

      const weekPlan: WeekPlan = {
        week: enhancedWeek,
        weekNumber: currentWeekInfo.weekNumber,
        year: currentWeekInfo.year,
        startDate: currentWeekInfo.startDate.toISOString(),
        endDate: currentWeekInfo.endDate.toISOString(),
        isActive: true,
        generatedAt: Date.now()
      };

      // Store the new plan
      await this.storeWeeklyPlan(weekPlan);
      await this.updateCurrentWeekInfo(currentWeekInfo);

      const endTime = performance.now();
      const totalTime = endTime - startTime;
      console.log(`🚀 PERFORMANCE: Weekly plan generation completed in ${totalTime.toFixed(2)}ms`);
      console.log(`✅ Generated and stored weekly plan for Week ${currentWeekInfo.weekNumber}`);

      // Performance metrics
      const mealsCount = enhancedWeek.reduce((total, day) => total + Object.keys(day.meals).length, 0);
      console.log(`📊 PERFORMANCE METRICS:
        - Total time: ${totalTime.toFixed(2)}ms
        - Meals processed: ${mealsCount}
        - Average time per meal: ${(totalTime / mealsCount).toFixed(2)}ms
        - Performance mode: OPTIMIZED (basic data only)`);

      return weekPlan;

    } catch (error) {
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      console.error(`❌ Error generating weekly plan in ${totalTime.toFixed(2)}ms:`, error);
      console.error('❌ Full error details:', error);
      return null;
    }
  }

  // Enhanced multi-layer plan storage with integrity verification
  async storeWeeklyPlan(weekPlan: WeekPlan): Promise<void> {
    const startTime = performance.now();
    console.log('💾 Starting multi-layer plan storage...');

    try {
      // Pre-storage validation and repair
      if (!this.validatePlanStructure(weekPlan, true)) {
        throw new Error('Plan failed validation and could not be repaired');
      }

      // Get existing plans with error handling
      let existingPlans: WeekPlan[] = [];
      try {
        const existingPlansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
        if (existingPlansJson) {
          existingPlans = JSON.parse(existingPlansJson);
          if (!Array.isArray(existingPlans)) {
            console.warn('⚠️ Invalid existing plans structure, starting fresh');
            existingPlans = [];
          }
        }
      } catch (parseError) {
        console.warn('⚠️ Could not parse existing plans, starting fresh:', parseError);
        existingPlans = [];
      }

      // Find if this plan already exists (for updates)
      const existingPlanIndex = existingPlans.findIndex(plan =>
        plan.weekNumber === weekPlan.weekNumber &&
        plan.year === weekPlan.year
      );

      if (existingPlanIndex !== -1) {
        // Update existing plan with merge strategy
        const existingPlan = existingPlans[existingPlanIndex];
        existingPlans[existingPlanIndex] = {
          ...existingPlan,
          ...weekPlan,
          // Preserve important metadata
          generatedAt: weekPlan.generatedAt || existingPlan.generatedAt,
          isActive: weekPlan.isActive !== undefined ? weekPlan.isActive : existingPlan.isActive,
          // Add update timestamp
          lastUpdated: Date.now()
        };
        console.log(`📝 Updated existing plan for Week ${weekPlan.weekNumber}, ${weekPlan.year}`);
      } else {
        // New plan - intelligent active status management
        if (weekPlan.isActive) {
          existingPlans.forEach(plan => {
            if (plan.isActive) {
              plan.isActive = false;
              console.log(`📝 Deactivated plan for Week ${plan.weekNumber}, ${plan.year}`);
            }
          });
        }

        // Add creation timestamp
        weekPlan.createdAt = weekPlan.createdAt || Date.now();
        existingPlans.push(weekPlan);
        console.log(`📝 Added new plan for Week ${weekPlan.weekNumber}, ${weekPlan.year}`);
      }

      // Intelligent cleanup with preservation of important plans
      const config = await this.getConfig();
      const currentWeekInfo = this.getCurrentWeekInfo();

      // Sort plans by importance: current week first, then by generation time
      const sortedPlans = existingPlans.sort((a, b) => {
        // Current week plans have highest priority
        const aIsCurrent = a.weekNumber === currentWeekInfo.weekNumber && a.year === currentWeekInfo.year;
        const bIsCurrent = b.weekNumber === currentWeekInfo.weekNumber && b.year === currentWeekInfo.year;

        if (aIsCurrent && !bIsCurrent) return -1;
        if (!aIsCurrent && bIsCurrent) return 1;

        // Active plans have higher priority
        if (a.isActive && !b.isActive) return -1;
        if (!a.isActive && b.isActive) return 1;

        // Then by generation time
        return b.generatedAt - a.generatedAt;
      });

      const plansToKeep = sortedPlans.slice(0, config.maxStoredWeeks);

      // OPTIMIZED: Single atomic write operation to prevent flashing
      const planData = JSON.stringify(plansToKeep);
      await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, planData);
      console.log('💾 PERSISTENCE DEBUG: Plan stored to AsyncStorage with key:', this.WEEKLY_PLANS_KEY);
      console.log('📊 PERSISTENCE DEBUG: Stored plan count:', plansToKeep.length);

      // REMOVED: Multiple storage operations that cause re-renders
      // - createPlanBackup: Not critical for meal replacement operations
      // - updateCurrentWeekInfo: Only needed for new plan generation, not meal updates
      // - createIntegrityCheckpoint: Can be done asynchronously without blocking UI

      // Background operations (non-blocking)
      Promise.all([
        this.createPlanBackup(weekPlan),
        weekPlan.isActive ? this.updateCurrentWeekInfo(this.getCurrentWeekInfo()) : Promise.resolve(),
        this.createIntegrityCheckpoint(weekPlan)
      ]).catch(error => {
        console.warn('⚠️ Background storage operations failed (non-critical):', error);
      });

      // LAYER 5: Database backup (if available)
      try {
        const DatabaseIntegrationService = (await import('./DatabaseIntegrationService')).default;
        await DatabaseIntegrationService.saveWeeklyPlanToDatabase(weekPlan);
        console.log('✅ Plan saved to database backup');
      } catch (dbError) {
        console.warn('⚠️ Database backup failed (non-critical):', dbError);
        await this.logRecoveryAction('DB_BACKUP_FAILED', `Database backup failed: ${dbError.message}`);
      }

      // LAYER 6: Verify storage integrity
      const verificationResult = await this.verifyStorageIntegrity(weekPlan);
      if (!verificationResult.isValid) {
        console.error('❌ Storage integrity verification failed:', verificationResult.errors);
        await this.logRecoveryAction('STORAGE_INTEGRITY_FAILED', `Verification failed: ${verificationResult.errors.join(', ')}`);

        // Attempt to fix integrity issues
        await this.repairStorageIntegrity(weekPlan);
      }

      const endTime = performance.now();
      console.log(`✅ Multi-layer plan storage completed in ${(endTime - startTime).toFixed(2)}ms`);
      await this.logRecoveryAction('STORAGE_SUCCESS', `Plan stored successfully in ${(endTime - startTime).toFixed(2)}ms`);

    } catch (error) {
      const endTime = performance.now();
      console.error(`❌ Error in multi-layer plan storage after ${(endTime - startTime).toFixed(2)}ms:`, error);

      // Emergency storage attempt
      try {
        console.log('🚨 Attempting emergency storage...');
        const emergencyKey = `${this.WEEKLY_PLANS_KEY}_emergency_${Date.now()}`;
        await AsyncStorage.setItem(emergencyKey, JSON.stringify([weekPlan]));
        await this.logRecoveryAction('EMERGENCY_STORAGE', `Plan saved to emergency key: ${emergencyKey}`);
        console.log('✅ Emergency storage successful');
      } catch (emergencyError) {
        console.error('❌ Emergency storage failed:', emergencyError);
        await this.logRecoveryAction('EMERGENCY_STORAGE_FAILED', `Emergency storage failed: ${emergencyError.message}`);
      }

      throw error;
    }
  }

  // Storage integrity verification
  private async verifyStorageIntegrity(expectedPlan: WeekPlan): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Verify primary storage
      const primaryData = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (!primaryData) {
        errors.push('Primary storage is empty');
      } else {
        try {
          const plans = JSON.parse(primaryData);
          if (!Array.isArray(plans)) {
            errors.push('Primary storage contains invalid data structure');
          } else {
            const expectedPlanExists = plans.some(plan =>
              plan.weekNumber === expectedPlan.weekNumber &&
              plan.year === expectedPlan.year
            );

            if (!expectedPlanExists) {
              errors.push('Expected plan not found in primary storage');
            }
          }
        } catch (parseError) {
          errors.push('Primary storage contains corrupted JSON');
        }
      }

      // Verify backup storage
      const backupData = await AsyncStorage.getItem(this.BACKUP_KEY);
      if (!backupData) {
        warnings.push('Backup storage is empty');
      } else {
        try {
          const backup = JSON.parse(backupData);
          if (!backup.plans || !Array.isArray(backup.plans)) {
            warnings.push('Backup storage has invalid structure');
          }
        } catch (parseError) {
          warnings.push('Backup storage contains corrupted JSON');
        }
      }

      // Verify current week info
      const weekInfo = await AsyncStorage.getItem(this.CURRENT_WEEK_KEY);
      if (!weekInfo) {
        warnings.push('Current week info is missing');
      } else {
        try {
          const info = JSON.parse(weekInfo);
          if (!info.weekNumber || !info.year) {
            warnings.push('Current week info has invalid structure');
          }
        } catch (parseError) {
          warnings.push('Current week info contains corrupted JSON');
        }
      }

      // Verify integrity checkpoint
      const checkpoint = await AsyncStorage.getItem(this.INTEGRITY_CHECK_KEY);
      if (!checkpoint) {
        warnings.push('Integrity checkpoint is missing');
      } else {
        try {
          const checkpointData = JSON.parse(checkpoint);
          if (!checkpointData.lastValidPlan || !checkpointData.checksum) {
            warnings.push('Integrity checkpoint has invalid structure');
          }
        } catch (parseError) {
          warnings.push('Integrity checkpoint contains corrupted JSON');
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings
      };

    } catch (error) {
      errors.push(`Verification failed: ${error.message}`);
      return { isValid: false, errors, warnings };
    }
  }

  // Repair storage integrity issues
  private async repairStorageIntegrity(plan: WeekPlan): Promise<void> {
    try {
      console.log('🔧 Attempting to repair storage integrity...');

      // Repair primary storage if corrupted
      try {
        const primaryData = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
        if (!primaryData) {
          await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify([plan]));
          console.log('✅ Repaired empty primary storage');
        } else {
          const plans = JSON.parse(primaryData);
          if (!Array.isArray(plans)) {
            await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify([plan]));
            console.log('✅ Repaired corrupted primary storage structure');
          }
        }
      } catch (primaryError) {
        console.error('❌ Could not repair primary storage:', primaryError);
        await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify([plan]));
      }

      // Repair backup storage
      try {
        await this.createPlanBackup(plan);
        console.log('✅ Repaired backup storage');
      } catch (backupError) {
        console.error('❌ Could not repair backup storage:', backupError);
      }

      // Repair current week info
      try {
        const currentWeekInfo = this.getCurrentWeekInfo();
        await this.updateCurrentWeekInfo(currentWeekInfo);
        console.log('✅ Repaired current week info');
      } catch (weekError) {
        console.error('❌ Could not repair current week info:', weekError);
      }

      // Repair integrity checkpoint
      try {
        await this.createIntegrityCheckpoint(plan);
        console.log('✅ Repaired integrity checkpoint');
      } catch (checkpointError) {
        console.error('❌ Could not repair integrity checkpoint:', checkpointError);
      }

      await this.logRecoveryAction('INTEGRITY_REPAIR', 'Storage integrity repair completed');

    } catch (error) {
      console.error('❌ Storage integrity repair failed:', error);
      await this.logRecoveryAction('INTEGRITY_REPAIR_FAILED', `Repair failed: ${error.message}`);
    }
  }

  // CRITICAL FIX: Add missing saveWeekPlan method that was being called but didn't exist
  async saveWeekPlan(weekPlan: WeekPlan): Promise<void> {
    try {
      console.log('💾 Saving week plan with current week info update...');

      // Store the plan
      await this.storeWeeklyPlan(weekPlan);

      // CRITICAL: Update current week info so isCurrentPlanValid() works correctly
      const currentWeekInfo = this.getCurrentWeekInfo();
      await this.updateCurrentWeekInfo(currentWeekInfo);

      console.log('✅ Week plan saved successfully with current week info updated');
    } catch (error) {
      console.error('❌ Error in saveWeekPlan:', error);
      throw error;
    }
  }

  // Update current week info with retry logic and verification
  private async updateCurrentWeekInfo(weekInfo: any): Promise<void> {
    let attempts = 0;
    const maxAttempts = 3;

    while (attempts < maxAttempts) {
      try {
        await AsyncStorage.setItem(this.CURRENT_WEEK_KEY, JSON.stringify(weekInfo));
        console.log(`📅 Updated current week info: Week ${weekInfo.weekNumber}, ${weekInfo.year}`);

        // Verify the save worked
        const verification = await AsyncStorage.getItem(this.CURRENT_WEEK_KEY);
        if (verification) {
          const parsed = JSON.parse(verification);
          if (parsed.weekNumber === weekInfo.weekNumber && parsed.year === weekInfo.year) {
            console.log('✅ Current week info save verified');
            return;
          }
        }

        throw new Error('Week info verification failed');
      } catch (error) {
        attempts++;
        console.error(`❌ Attempt ${attempts}/${maxAttempts} to update week info failed:`, error);

        if (attempts < maxAttempts) {
          console.log(`🔄 Retrying week info update in 500ms...`);
          await new Promise(resolve => setTimeout(resolve, 500));
        } else {
          console.error('❌ Failed to update current week info after all attempts');
          throw error;
        }
      }
    }
  }

  // Get active weekly plan with advanced recovery and graceful degradation
  async getActiveWeeklyPlan(): Promise<WeekPlan | null> {
    try {
      // First, try migration of legacy data
      await this.migrateLegacyData();

      const plansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      console.log('🔍 PERSISTENCE DEBUG: Checking AsyncStorage with key:', this.WEEKLY_PLANS_KEY);
      console.log('📊 PERSISTENCE DEBUG: Raw storage data:', plansJson ? 'Found data' : 'No data');

      if (!plansJson) {
        console.log('📅 No plans found in storage, checking for recovery options...');

        // Try recovery strategies before giving up
        const recoveredPlan = await this.attemptPlanRecovery('', 'NO_DATA');
        if (recoveredPlan) {
          await this.storeWeeklyPlan(recoveredPlan);
          return recoveredPlan;
        }

        return null;
      }

      let plans: WeekPlan[];
      try {
        plans = JSON.parse(plansJson);
      } catch (parseError) {
        console.error('❌ Corrupted plans data detected, attempting recovery...');

        // Instead of immediately clearing, try recovery first
        const recoveredPlan = await this.attemptPlanRecovery(plansJson, 'JSON_PARSE_ERROR');
        if (recoveredPlan) {
          // Store recovered plan and return it
          await this.storeWeeklyPlan(recoveredPlan);
          return recoveredPlan;
        }

        // Only clear if recovery completely failed
        console.warn('⚠️ Recovery failed, clearing corrupted data as last resort');
        await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
        await this.logRecoveryAction('CLEAR_CORRUPTED', 'All recovery attempts failed');
        return null;
      }

      // Validate and repair plans array
      if (!Array.isArray(plans)) {
        console.error('❌ Invalid plans data structure, attempting recovery...');

        const recoveredPlan = await this.attemptPlanRecovery(plansJson, 'INVALID_STRUCTURE');
        if (recoveredPlan) {
          await this.storeWeeklyPlan(recoveredPlan);
          return recoveredPlan;
        }

        // Last resort: clear invalid data
        await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
        await this.logRecoveryAction('CLEAR_INVALID', 'Invalid structure, recovery failed');
        return null;
      }

      // Validate each plan and attempt repairs
      const validPlans = [];
      for (const plan of plans) {
        if (this.validatePlanStructure(plan, true)) {
          validPlans.push(plan);
        } else {
          console.warn('⚠️ Removing invalid plan that could not be repaired');
        }
      }

      if (validPlans.length === 0) {
        console.log('📅 No valid plans found after validation, attempting recovery...');
        const recoveredPlan = await this.attemptPlanRecovery(plansJson, 'NO_VALID_PLANS');
        if (recoveredPlan) {
          await this.storeWeeklyPlan(recoveredPlan);
          return recoveredPlan;
        }
        return null;
      }

      // Update storage with validated plans if any were repaired
      if (validPlans.length !== plans.length) {
        await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(validPlans));
        await this.logRecoveryAction('PLAN_VALIDATION', `Repaired ${plans.length - validPlans.length} invalid plans`);
      }

      // Find active plans
      const activePlans = validPlans.filter(plan => plan.isActive);

      if (activePlans.length === 0) {
        console.log('📅 No active plans found, checking for recent plans...');

        // Try to activate the most recent plan
        if (validPlans.length > 0) {
          const mostRecent = validPlans.sort((a, b) => b.generatedAt - a.generatedAt)[0];
          mostRecent.isActive = true;

          await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(validPlans));
          await this.logRecoveryAction('ACTIVATE_RECENT', 'Activated most recent plan');

          console.log('✅ Activated most recent plan as fallback');
          return mostRecent;
        }

        return null;
      }

      if (activePlans.length > 1) {
        console.warn('⚠️ Multiple active plans found, resolving conflict...');

        // Intelligent conflict resolution
        const currentWeekInfo = this.getCurrentWeekInfo();

        // Prefer plan for current week
        const currentWeekPlan = activePlans.find(plan =>
          plan.weekNumber === currentWeekInfo.weekNumber &&
          plan.year === currentWeekInfo.year
        );

        if (currentWeekPlan) {
          // Deactivate others
          validPlans.forEach(plan => {
            if (plan.isActive && plan !== currentWeekPlan) {
              plan.isActive = false;
            }
          });

          await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(validPlans));
          await this.logRecoveryAction('RESOLVE_CONFLICT', 'Selected current week plan');

          return currentWeekPlan;
        } else {
          // Use most recent if no current week plan
          const mostRecent = activePlans.sort((a, b) => b.generatedAt - a.generatedAt)[0];

          validPlans.forEach(plan => {
            if (plan.isActive && plan !== mostRecent) {
              plan.isActive = false;
            }
          });

          await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(validPlans));
          await this.logRecoveryAction('RESOLVE_CONFLICT', 'Selected most recent plan');

          return mostRecent;
        }
      }

      const activePlan = activePlans[0];

      // Final validation with auto-repair
      if (!this.validatePlanStructure(activePlan, true)) {
        console.error('❌ Active plan failed validation and could not be repaired');

        // Try one more recovery attempt
        const recoveredPlan = await this.attemptPlanRecovery(JSON.stringify(activePlan), 'VALIDATION_FAILED');
        if (recoveredPlan) {
          await this.storeWeeklyPlan(recoveredPlan);
          return recoveredPlan;
        }

        return null;
      }

      // Create backup of successfully retrieved plan
      await this.createPlanBackup(activePlan);

      console.log(`📅 Found active plan: Week ${activePlan.weekNumber}, ${activePlan.year}`);
      return activePlan;

    } catch (error) {
      console.error('❌ Error getting active weekly plan:', error);

      // Final recovery attempt on critical error
      try {
        const emergencyPlan = await this.attemptPlanRecovery('', 'CRITICAL_ERROR');
        if (emergencyPlan) {
          await this.storeWeeklyPlan(emergencyPlan);
          await this.logRecoveryAction('EMERGENCY_RECOVERY', 'Critical error recovery successful');
          return emergencyPlan;
        }
      } catch (recoveryError) {
        console.error('❌ Emergency recovery failed:', recoveryError);
      }

      return null;
    }
  }

  // Get configuration
  private async getConfig(): Promise<WeeklyPlanConfig> {
    try {
      const configJson = await AsyncStorage.getItem(this.CONFIG_KEY);
      if (configJson) {
        return JSON.parse(configJson);
      }
      
      // Default configuration
      const defaultConfig: WeeklyPlanConfig = {
        autoGenerate: true,
        generateDaysAhead: 2, // Generate new plan 2 days before current week ends
        maxStoredWeeks: 8 // Keep 8 weeks of history
      };
      
      await AsyncStorage.setItem(this.CONFIG_KEY, JSON.stringify(defaultConfig));
      return defaultConfig;
    } catch (error) {
      console.error('❌ Error getting config:', error);
      return {
        autoGenerate: true,
        generateDaysAhead: 2,
        maxStoredWeeks: 8
      };
    }
  }

  // Check if we need to generate next week's plan
  async shouldGenerateNextWeek(): Promise<boolean> {
    try {
      const config = await this.getConfig();
      if (!config.autoGenerate) return false;
      
      const currentWeekInfo = this.getCurrentWeekInfo();
      const now = new Date();
      const daysUntilWeekEnd = Math.ceil((currentWeekInfo.endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      console.log(`📅 Days until week end: ${daysUntilWeekEnd}`);
      console.log(`📅 Generate days ahead setting: ${config.generateDaysAhead}`);
      
      return daysUntilWeekEnd <= config.generateDaysAhead;
    } catch (error) {
      console.error('❌ Error checking if should generate next week:', error);
      return false;
    }
  }

  // Main function to ensure current week has a valid plan
  async ensureCurrentWeekPlan(userProfile: any): Promise<WeekPlan | null> {
    try {
      console.log('📅 Ensuring current week has valid plan...');

      if (!userProfile) {
        console.error('❌ No user profile provided, cannot ensure plan');
        return null;
      }

      // Check if current plan is valid
      const isValid = await this.isCurrentPlanValid();

      if (isValid) {
        console.log('📅 Current plan is valid, returning existing plan');
        const existingPlan = await this.getActiveWeeklyPlan();

        if (existingPlan) {
          // Double-check the plan is actually for the current week
          const currentWeekInfo = this.getCurrentWeekInfo();
          if (existingPlan.weekNumber === currentWeekInfo.weekNumber &&
              existingPlan.year === currentWeekInfo.year) {
            return existingPlan;
          } else {
            console.log('📅 Plan week mismatch, regenerating');
          }
        }
      }

      console.log('📅 Current plan is invalid or missing, generating new plan');

      // Try to generate new plan with retry logic
      let attempts = 0;
      const maxAttempts = 3;

      while (attempts < maxAttempts) {
        try {
          const newPlan = await this.generateWeeklyPlan(userProfile);
          if (newPlan) {
            console.log('✅ Successfully generated new weekly plan');
            return newPlan;
          }
        } catch (generateError) {
          attempts++;
          console.error(`❌ Attempt ${attempts}/${maxAttempts} to generate plan failed:`, generateError);

          if (attempts < maxAttempts) {
            console.log(`🔄 Retrying plan generation in 1 second...`);
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      }

      console.error('❌ Failed to generate plan after all attempts');
      return null;

    } catch (error) {
      console.error('❌ Error ensuring current week plan:', error);
      return null;
    }
  }

  // Get plan history for analytics
  async getPlanHistory(limit: number = 4): Promise<WeekPlan[]> {
    try {
      const plansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (!plansJson) return [];

      const plans: WeekPlan[] = JSON.parse(plansJson);
      return plans
        .sort((a, b) => b.generatedAt - a.generatedAt)
        .slice(0, limit);
    } catch (error) {
      console.error('❌ Error getting plan history:', error);
      return [];
    }
  }

  // Intelligent maintenance cleanup with graduated response levels
  async performMaintenanceCleanup(): Promise<void> {
    try {
      console.log('🧹 Performing intelligent weekly plan maintenance...');

      // First, try migration of any legacy data
      const migrationPerformed = await this.migrateLegacyData();
      if (migrationPerformed) {
        console.log('✅ Legacy data migration completed during maintenance');
      }

      const plansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (!plansJson) {
        console.log('🧹 No plans found for cleanup');

        // Check for any recoverable data before giving up
        const recoveredPlan = await this.attemptPlanRecovery('', 'NO_DATA_MAINTENANCE');
        if (recoveredPlan) {
          await this.storeWeeklyPlan(recoveredPlan);
          console.log('✅ Recovered plan during maintenance cleanup');
        }

        return;
      }

      let plans: WeekPlan[];
      try {
        plans = JSON.parse(plansJson);
      } catch (parseError) {
        console.warn('⚠️ Corrupted plans data detected during cleanup, attempting recovery...');

        // Try recovery before clearing
        const recoveredPlan = await this.attemptPlanRecovery(plansJson, 'MAINTENANCE_PARSE_ERROR');
        if (recoveredPlan) {
          await this.storeWeeklyPlan(recoveredPlan);
          await this.logRecoveryAction('MAINTENANCE_RECOVERY', 'Recovered from corrupted data during cleanup');
          console.log('✅ Successfully recovered from corrupted data during maintenance');
          return;
        }

        // Only clear if recovery completely failed
        console.warn('⚠️ Recovery failed during maintenance, clearing as last resort');
        await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
        await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);
        await this.logRecoveryAction('MAINTENANCE_CLEAR', 'Cleared corrupted data after failed recovery');
        return;
      }

      // Validate and repair plans array structure
      if (!Array.isArray(plans)) {
        console.warn('⚠️ Invalid plans data structure during cleanup, attempting recovery...');

        const recoveredPlan = await this.attemptPlanRecovery(plansJson, 'MAINTENANCE_INVALID_STRUCTURE');
        if (recoveredPlan) {
          await this.storeWeeklyPlan(recoveredPlan);
          await this.logRecoveryAction('MAINTENANCE_STRUCTURE_RECOVERY', 'Recovered from invalid structure');
          return;
        }

        // Clear only if recovery failed
        await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
        await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);
        await this.logRecoveryAction('MAINTENANCE_STRUCTURE_CLEAR', 'Cleared invalid structure after failed recovery');
        return;
      }

      const currentWeekInfo = this.getCurrentWeekInfo();
      const currentTime = Date.now();
      const oneYearAgo = currentTime - (365 * 24 * 60 * 60 * 1000);

      // Intelligent plan filtering with repair attempts
      const repairedPlans: WeekPlan[] = [];
      const removedPlans: any[] = [];

      for (const plan of plans) {
        // Try to repair invalid plans instead of immediately removing them
        if (!plan || typeof plan.generatedAt !== 'number' ||
            typeof plan.weekNumber !== 'number' ||
            typeof plan.year !== 'number' ||
            !Array.isArray(plan.week)) {

          console.warn('🧹 Found invalid plan structure, attempting repair...');

          if (this.validatePlanStructure(plan, true)) {
            console.log('✅ Successfully repaired invalid plan structure');
            repairedPlans.push(plan);
            continue;
          } else {
            console.warn('⚠️ Could not repair plan structure, removing');
            removedPlans.push(plan);
            continue;
          }
        }

        const planAge = currentTime - plan.generatedAt;
        const sixMonthsAgo = currentTime - (6 * 30 * 24 * 60 * 60 * 1000); // More generous than 8 weeks

        // More lenient age filtering
        if (plan.generatedAt > oneYearAgo) {
          // Keep active plans regardless of age (within reason)
          if (plan.isActive) {
            repairedPlans.push(plan);
            continue;
          }

          // Keep inactive plans for 6 months instead of 8 weeks
          if (plan.generatedAt > sixMonthsAgo) {
            repairedPlans.push(plan);
            continue;
          }

          // Keep current week plans even if old
          if (plan.weekNumber === currentWeekInfo.weekNumber && plan.year === currentWeekInfo.year) {
            console.log('🧹 Keeping old current week plan for recovery');
            repairedPlans.push(plan);
            continue;
          }
        }

        // Remove very old plans
        removedPlans.push(plan);
      }

      // Intelligent active plan management
      const activePlans = repairedPlans.filter(plan => plan.isActive);

      if (activePlans.length > 1) {
        console.warn('🧹 Multiple active plans found during cleanup, resolving intelligently...');

        // Prefer current week plan
        const currentWeekPlan = activePlans.find(plan =>
          plan.weekNumber === currentWeekInfo.weekNumber &&
          plan.year === currentWeekInfo.year
        );

        if (currentWeekPlan) {
          // Deactivate all others
          repairedPlans.forEach(plan => {
            if (plan.isActive && plan !== currentWeekPlan) {
              plan.isActive = false;
            }
          });
          console.log('✅ Activated current week plan, deactivated others');
        } else {
          // Use most recent if no current week plan
          const mostRecent = activePlans.sort((a, b) => b.generatedAt - a.generatedAt)[0];
          repairedPlans.forEach(plan => {
            if (plan.isActive && plan !== mostRecent) {
              plan.isActive = false;
            }
          });
          console.log('✅ Activated most recent plan, deactivated others');
        }
      }

      // Ensure current week has an active plan
      const hasCurrentWeekPlan = repairedPlans.some(plan =>
        plan.weekNumber === currentWeekInfo.weekNumber &&
        plan.year === currentWeekInfo.year &&
        plan.isActive
      );

      if (!hasCurrentWeekPlan) {
        console.log('⚠️ No active plan for current week found during cleanup, checking for alternatives...');

        // Try to find and activate any plan for current week
        const currentWeekPlan = repairedPlans.find(plan =>
          plan.weekNumber === currentWeekInfo.weekNumber &&
          plan.year === currentWeekInfo.year
        );

        if (currentWeekPlan) {
          currentWeekPlan.isActive = true;
          console.log('✅ Activated existing current week plan during cleanup');
        } else {
          console.log('⚠️ No plan found for current week - will need regeneration');
        }
      }

      // Create backup before saving cleaned data
      if (repairedPlans.length > 0) {
        const activePlan = repairedPlans.find(plan => plan.isActive);
        if (activePlan) {
          await this.createPlanBackup(activePlan);
        }
      }

      // Save cleaned and repaired plans
      await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(repairedPlans));

      // Log cleanup results
      const cleanupStats = {
        totalProcessed: plans.length,
        kept: repairedPlans.length,
        removed: removedPlans.length,
        repaired: repairedPlans.filter(plan => plan._wasRepaired).length
      };

      console.log(`✅ Intelligent cleanup complete:`, cleanupStats);
      await this.logRecoveryAction('MAINTENANCE_COMPLETE',
        `Processed ${cleanupStats.totalProcessed}, kept ${cleanupStats.kept}, removed ${cleanupStats.removed}, repaired ${cleanupStats.repaired}`);

    } catch (error) {
      console.error('❌ Error during maintenance cleanup:', error);

      // Graduated error response instead of immediate clearing
      try {
        // First, try to preserve any valid data
        const emergencyPlan = await this.attemptPlanRecovery('', 'MAINTENANCE_CRITICAL_ERROR');
        if (emergencyPlan) {
          await this.storeWeeklyPlan(emergencyPlan);
          await this.logRecoveryAction('MAINTENANCE_EMERGENCY_RECOVERY', 'Recovered from critical maintenance error');
          console.log('✅ Emergency recovery successful during maintenance failure');
          return;
        }

        // Only clear if absolutely no recovery is possible
        console.warn('⚠️ All recovery attempts failed, clearing data as absolute last resort');
        await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
        await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);
        await this.logRecoveryAction('MAINTENANCE_CLEAR_LAST_RESORT', 'Cleared all data after complete failure');

      } catch (clearError) {
        console.error('❌ Failed to clear corrupted data:', clearError);
        await this.logRecoveryAction('MAINTENANCE_CLEAR_FAILED', 'Could not clear corrupted data');
      }
    }
  }

  // Initialize weekly plan system - call this on app startup
  async initializeWeeklyPlanSystem(userProfile: any): Promise<WeekPlan | null> {
    try {
      console.log('🚀 Initializing weekly plan system...');

      if (!userProfile) {
        console.error('❌ No user profile provided for initialization');
        return null;
      }

      // Perform maintenance cleanup first with error handling
      try {
        await this.performMaintenanceCleanup();
        console.log('✅ Maintenance cleanup completed');
      } catch (cleanupError) {
        console.error('❌ Maintenance cleanup failed (non-critical):', cleanupError);
        // Continue initialization even if cleanup fails
      }

      // Ensure current week has a valid plan
      const currentPlan = await this.ensureCurrentWeekPlan(userProfile);

      if (!currentPlan) {
        console.error('❌ Failed to ensure current week plan');
        return null;
      }

      // Validate and fix nutrition data for existing plan
      try {
        await this.validateAndFixNutritionData(currentPlan.week);
        // Save the plan if any nutrition data was fixed
        await this.saveWeekPlan(currentPlan);
        console.log('✅ Nutrition data validated and plan saved');
      } catch (nutritionError) {
        console.error('❌ Error validating nutrition data (non-critical):', nutritionError);
        // Continue even if nutrition validation fails
      }

      // Check if we should pre-generate next week
      try {
        const shouldGenerateNext = await this.shouldGenerateNextWeek();
        if (shouldGenerateNext && userProfile) {
          console.log('📅 Pre-generating next week\'s plan in background...');
          // Don't await this - let it run in background
          this.generateNextWeekPlan(userProfile).catch(error => {
            console.error('❌ Error pre-generating next week:', error);
          });
        }
      } catch (nextWeekError) {
        console.error('❌ Error checking next week generation (non-critical):', nextWeekError);
      }

      console.log('✅ Weekly plan system initialized successfully');
      return currentPlan;

    } catch (error) {
      console.error('❌ Critical error initializing weekly plan system:', error);

      // Try to recover by returning any existing plan
      try {
        const fallbackPlan = await this.getActiveWeeklyPlan();
        if (fallbackPlan) {
          console.log('🔄 Returning fallback plan from storage');
          return fallbackPlan;
        }
      } catch (fallbackError) {
        console.error('❌ Fallback plan retrieval failed:', fallbackError);
      }

      return null;
    }
  }

  // Emergency recovery method to ensure app always has a plan
  async emergencyPlanRecovery(userProfile: any): Promise<WeekPlan | null> {
    try {
      console.log('🚨 Emergency plan recovery initiated...');

      // Clear potentially corrupted data
      await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
      await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);

      // Force generate a new plan
      const newPlan = await this.generateWeeklyPlan(userProfile);

      if (newPlan) {
        console.log('✅ Emergency plan recovery successful');
        return newPlan;
      } else {
        console.error('❌ Emergency plan recovery failed');
        return null;
      }
    } catch (error) {
      console.error('❌ Emergency plan recovery error:', error);
      return null;
    }
  }

  // Generate next week's plan in advance
  private async generateNextWeekPlan(userProfile: any): Promise<void> {
    try {
      const currentWeekInfo = this.getCurrentWeekInfo();
      const nextWeekInfo = {
        weekNumber: currentWeekInfo.weekNumber === 53 ? 1 : currentWeekInfo.weekNumber + 1,
        year: currentWeekInfo.weekNumber === 53 ? currentWeekInfo.year + 1 : currentWeekInfo.year,
        startDate: new Date(currentWeekInfo.startDate.getTime() + (7 * 24 * 60 * 60 * 1000)),
        endDate: new Date(currentWeekInfo.endDate.getTime() + (7 * 24 * 60 * 60 * 1000))
      };

      // Check if next week already has a plan
      const existingPlans = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (existingPlans) {
        const plans: WeekPlan[] = JSON.parse(existingPlans);
        const hasNextWeekPlan = plans.some(plan =>
          plan.weekNumber === nextWeekInfo.weekNumber &&
          plan.year === nextWeekInfo.year
        );

        if (hasNextWeekPlan) {
          console.log('📅 Next week already has a plan, skipping generation');
          return;
        }
      }

      console.log(`📅 Generating plan for next week: Week ${nextWeekInfo.weekNumber}, ${nextWeekInfo.year}`);

      // Generate the plan for next week
      const apiOptions = {
        dietaryRestrictions: userProfile.dietaryRestrictions || [],
        calorieGoal: userProfile.caloriesGoal || userProfile.dailyCalorieGoal || 2000,
        mealsPerDay: 3,
        preferences: userProfile.dietaryPreferences || userProfile.foodPreferences || [],
        allergies: userProfile.allergies || [],
        preferredCuisines: userProfile.preferredCuisines || [],
        activityLevel: userProfile.activityLevel || 'Moderate',
        healthGoals: [
          ...(userProfile.healthGoals || []),
          ...(userProfile.fitnessObjectives || []),
          ...(userProfile.healthConditions || []),
          userProfile.weightGoal ? `${userProfile.weightGoal} weight` : ''
        ].filter(Boolean)
      };

      console.log('🎯 API Options for meal plan generation:', JSON.stringify(apiOptions, null, 2));

      const mealPlanResult = await ApiService.generateWeeklyMealPlan(apiOptions);

      if (mealPlanResult && mealPlanResult.week) {
        // Enhance next week's plan with complete data
        const enhancedNextWeek = await this.enhanceMealPlanWithData(mealPlanResult.week);

        const nextWeekPlan: WeekPlan = {
          week: enhancedNextWeek,
          weekNumber: nextWeekInfo.weekNumber,
          year: nextWeekInfo.year,
          startDate: nextWeekInfo.startDate.toISOString(),
          endDate: nextWeekInfo.endDate.toISOString(),
          isActive: false, // Not active until next week starts
          generatedAt: Date.now()
        };

        // Store the next week's plan using proper storage method
        await this.storeWeeklyPlan(nextWeekPlan);

        console.log(`✅ Pre-generated plan for Week ${nextWeekInfo.weekNumber}, ${nextWeekInfo.year}`);
      }

    } catch (error) {
      console.error('❌ Error generating next week plan:', error);
    }
  }

  // Clear all cached plans and force regeneration with fresh AI content
  async clearAllPlans(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
      await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);

      // Also clear recipe cache to ensure fresh AI generation
      await RecipeCacheService.clearAllCache();

      console.log('🧹 All weekly plans and recipe cache cleared - fresh AI content will be generated');
    } catch (error) {
      console.error('❌ Error clearing weekly plans:', error);
    }
  }

  // Deactivate a specific week plan
  private async deactivateWeek(weekNumber: number, year: number): Promise<void> {
    try {
      const plansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (!plansJson) return;

      const plans: WeekPlan[] = JSON.parse(plansJson);
      let planFound = false;

      plans.forEach(plan => {
        if (plan.weekNumber === weekNumber && plan.year === year) {
          plan.isActive = false;
          planFound = true;
        }
      });

      if (planFound) {
        await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(plans));
        console.log(`✅ Deactivated plan for Week ${weekNumber}, ${year}`);
      }
    } catch (error) {
      console.error('❌ Error deactivating week plan:', error);
    }
  }

  // Force regenerate current week with fresh AI content
  async forceRegenerateCurrentWeek(userProfile: any): Promise<WeekPlan | null> {
    try {
      console.log('🔄 Force regenerating current week with fresh AI content...');

      // Clear current week data
      const currentWeekInfo = this.getCurrentWeekInfo();
      await this.deactivateWeek(currentWeekInfo.weekNumber, currentWeekInfo.year);

      // Clear related recipe cache
      await RecipeCacheService.clearAllCache();

      // Generate fresh plan
      return await this.generateWeeklyPlan(userProfile);
    } catch (error) {
      console.error('❌ Error force regenerating current week:', error);
      return null;
    }
  }

  // Validate and fix nutrition data for weekly plan meals
  private async validateAndFixNutritionData(weekData: Array<{day: string; meals: {[key: string]: MealData}}>): Promise<void> {
    try {
      console.log('🔍 Validating nutrition data for weekly plan meals...');

      for (const day of weekData) {
        for (const [mealType, mealData] of Object.entries(day.meals)) {
          // Check if nutrition data looks suspicious (e.g., all meals have same calories)
          const needsRegeneration = this.shouldRegenerateNutrition(mealData, mealType);

          if (needsRegeneration) {
            try {
              console.log(`🔄 Regenerating nutrition for suspicious data: ${mealData.name} (${mealType})`);

              // Force regenerate recipe data
              const newRecipe = await RecipeCacheService.forceRegenerateRecipe(mealData.name, 'weekly_plan');

              // Update meal data with new nutrition
              day.meals[mealType] = {
                ...mealData,
                calories: newRecipe.nutrition.calories,
                protein: newRecipe.nutrition.protein,
                carbs: newRecipe.nutrition.carbs,
                fat: newRecipe.nutrition.fat
              };

              console.log(`✅ Fixed nutrition for ${mealData.name}:`, {
                calories: newRecipe.nutrition.calories,
                protein: newRecipe.nutrition.protein
              });
            } catch (error) {
              console.error(`❌ Error fixing nutrition for ${mealData.name}:`, error);
            }
          }
        }
      }

      console.log('✅ Nutrition data validation completed');

    } catch (error) {
      console.error('❌ Error validating nutrition data:', error);
    }
  }

  // Balance weekly nutrition to meet user's daily goals
  private async balanceWeeklyNutrition(weekData: Array<{day: string; meals: {[key: string]: MealData}}>, userProfile: any): Promise<void> {
    try {
      console.log('⚖️ Balancing weekly nutrition to meet user goals...');

      const targetCalories = userProfile.caloriesGoal || userProfile.dailyCalorieGoal || 2000;
      const targetProtein = userProfile.proteinGoal || 150;

      console.log(`🎯 Target daily goals: ${targetCalories} calories, ${targetProtein}g protein`);

      for (const day of weekData) {
        // Calculate current day's totals
        let dayCalories = 0;
        let dayProtein = 0;

        for (const mealData of Object.values(day.meals)) {
          dayCalories += mealData.calories || 0;
          dayProtein += mealData.protein || 0;
        }

        console.log(`📊 ${day.day} current: ${dayCalories} cal, ${dayProtein}g protein`);

        // Calculate adjustment factors
        const calorieAdjustment = targetCalories / Math.max(dayCalories, 1);
        const proteinAdjustment = targetProtein / Math.max(dayProtein, 1);

        // Apply balanced adjustments (don't go too extreme)
        const maxAdjustment = 1.3; // Max 30% adjustment
        const minAdjustment = 0.7; // Min 70% of original

        const finalCalorieAdjustment = Math.min(maxAdjustment, Math.max(minAdjustment, calorieAdjustment));
        const finalProteinAdjustment = Math.min(maxAdjustment, Math.max(minAdjustment, proteinAdjustment));

        // Apply adjustments to each meal
        for (const [mealType, mealData] of Object.entries(day.meals)) {
          const originalCalories = mealData.calories;
          const originalProtein = mealData.protein;

          // Adjust calories and protein proportionally
          mealData.calories = Math.round(originalCalories * finalCalorieAdjustment);
          mealData.protein = Math.round(originalProtein * finalProteinAdjustment);

          // Recalculate carbs and fat to maintain realistic ratios
          const proteinCalories = mealData.protein * 4;
          const remainingCalories = mealData.calories - proteinCalories;

          // Split remaining calories between carbs (60%) and fat (40%)
          const carbCalories = remainingCalories * 0.6;
          const fatCalories = remainingCalories * 0.4;

          mealData.carbs = Math.round(carbCalories / 4); // 4 cal/g for carbs
          mealData.fat = Math.round(fatCalories / 9);    // 9 cal/g for fat

          // Ensure minimum values
          mealData.carbs = Math.max(mealData.carbs, 10);
          mealData.fat = Math.max(mealData.fat, 5);
        }

        // Recalculate final totals
        const finalCalories = Object.values(day.meals).reduce((sum, meal) => sum + meal.calories, 0);
        const finalProtein = Object.values(day.meals).reduce((sum, meal) => sum + meal.protein, 0);

        console.log(`✅ ${day.day} balanced: ${finalCalories} cal (${((finalCalories/targetCalories)*100).toFixed(1)}%), ${finalProtein}g protein (${((finalProtein/targetProtein)*100).toFixed(1)}%)`);
      }

      console.log('✅ Weekly nutrition balancing completed');

    } catch (error) {
      console.error('❌ Error balancing weekly nutrition:', error);
    }
  }

  // Check if nutrition data should be regenerated
  private shouldRegenerateNutrition(mealData: MealData, mealType: string): boolean {
    // Check for suspicious patterns that indicate static/fallback data
    const calories = mealData.calories;
    const protein = mealData.protein;

    // Flag meals with exactly 350 calories (old static value)
    if (calories === 350) {
      console.log(`⚠️ Found suspicious 350 calorie meal: ${mealData.name}`);
      return true;
    }

    // Flag meals with very low protein (less than 10g for main meals)
    if (['breakfast', 'lunch', 'dinner'].includes(mealType.toLowerCase()) && protein < 10) {
      console.log(`⚠️ Found low protein meal: ${mealData.name} (${protein}g)`);
      return true;
    }

    // Flag meals with unrealistic protein ratios
    const proteinCalories = protein * 4;
    const proteinPercentage = (proteinCalories / calories) * 100;
    if (proteinPercentage < 10 || proteinPercentage > 50) {
      console.log(`⚠️ Found unrealistic protein ratio: ${mealData.name} (${proteinPercentage.toFixed(1)}%)`);
      return true;
    }

    return false;
  }

  // Force regenerate nutrition data for existing weekly plan meals
  async forceRegenerateNutritionData(): Promise<void> {
    try {
      console.log('🔄 Force regenerating nutrition data for all weekly plan meals...');

      const currentPlan = await this.getActiveWeeklyPlan();
      if (!currentPlan) {
        console.log('⚠️ No current week plan found to regenerate nutrition data');
        return;
      }

      // Regenerate nutrition data for each meal
      for (const day of currentPlan.week) {
        for (const [mealType, mealData] of Object.entries(day.meals)) {
          try {
            console.log(`🔄 Regenerating nutrition for: ${mealData.name} (${mealType})`);

            // Force regenerate recipe data
            const newRecipe = await RecipeCacheService.forceRegenerateRecipe(mealData.name, 'weekly_plan');

            // Update meal data with new nutrition
            day.meals[mealType] = {
              ...mealData,
              calories: newRecipe.nutrition.calories,
              protein: newRecipe.nutrition.protein,
              carbs: newRecipe.nutrition.carbs,
              fat: newRecipe.nutrition.fat
            };

            console.log(`✅ Updated nutrition for ${mealData.name}:`, {
              calories: newRecipe.nutrition.calories,
              protein: newRecipe.nutrition.protein
            });
          } catch (error) {
            console.error(`❌ Error regenerating nutrition for ${mealData.name}:`, error);
          }
        }
      }

      // Save updated plan
      await this.saveWeekPlan(currentPlan);
      console.log('✅ All nutrition data regenerated and saved');

    } catch (error) {
      console.error('❌ Error force regenerating nutrition data:', error);
    }
  }

  // Public plan state management methods
  async getPlanState(): Promise<{
    hasActivePlan: boolean;
    currentWeekPlan: WeekPlan | null;
    totalPlans: number;
    lastUpdate: string | null;
    recoveryStats: any;
  }> {
    try {
      const activePlan = await this.getActiveWeeklyPlan();
      const allPlans = await this.getPlanHistory(20);
      const recoveryStats = await this.getRecoveryStats();

      const currentWeekInfo = this.getCurrentWeekInfo();
      const currentWeekPlan = activePlan &&
        activePlan.weekNumber === currentWeekInfo.weekNumber &&
        activePlan.year === currentWeekInfo.year ? activePlan : null;

      return {
        hasActivePlan: !!activePlan,
        currentWeekPlan,
        totalPlans: allPlans.length,
        lastUpdate: activePlan ? new Date(activePlan.generatedAt).toISOString() : null,
        recoveryStats
      };
    } catch (error) {
      console.error('❌ Error getting plan state:', error);
      return {
        hasActivePlan: false,
        currentWeekPlan: null,
        totalPlans: 0,
        lastUpdate: null,
        recoveryStats: { totalRecoveries: 0, lastRecovery: null, successRate: 0, commonIssues: [] }
      };
    }
  }

  // Force plan state refresh with recovery
  async refreshPlanState(userProfile: any): Promise<WeekPlan | null> {
    try {
      console.log('🔄 Forcing plan state refresh...');

      // First try to get existing valid plan
      const existingPlan = await this.getActiveWeeklyPlan();
      if (existingPlan) {
        const isValid = await this.isCurrentPlanValid();
        if (isValid) {
          console.log('✅ Existing plan is valid, no refresh needed');
          return existingPlan;
        }
      }

      // Try to ensure current week plan
      const currentPlan = await this.ensureCurrentWeekPlan(userProfile);
      if (currentPlan) {
        console.log('✅ Plan state refreshed successfully');
        return currentPlan;
      }

      // Last resort: emergency recovery
      console.log('🚨 Attempting emergency plan recovery...');
      const recoveredPlan = await this.emergencyPlanRecovery(userProfile);

      return recoveredPlan;
    } catch (error) {
      console.error('❌ Plan state refresh failed:', error);
      await this.logRecoveryAction('STATE_REFRESH_FAILED', `Refresh failed: ${error.message}`);
      return null;
    }
  }

  // Emergency plan recovery (public method)
  async emergencyPlanRecovery(userProfile: any): Promise<WeekPlan | null> {
    try {
      console.log('🚨 Emergency plan recovery initiated...');

      // Clear potentially corrupted data
      await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
      await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);

      // Force generate a new plan
      const newPlan = await this.generateWeeklyPlan(userProfile);
      if (newPlan) {
        await this.storeWeeklyPlan(newPlan);
        await this.logRecoveryAction('EMERGENCY_RECOVERY_SUCCESS', 'Emergency recovery successful');
        console.log('✅ Emergency recovery completed');
        return newPlan;
      }

      await this.logRecoveryAction('EMERGENCY_RECOVERY_FAILED', 'Emergency recovery failed');
      return null;
    } catch (error) {
      console.error('❌ Emergency recovery failed:', error);
      await this.logRecoveryAction('EMERGENCY_RECOVERY_ERROR', `Emergency recovery error: ${error.message}`);
      return null;
    }
  }

  // Plan conflict resolution
  async resolvePlanConflicts(): Promise<boolean> {
    try {
      console.log('🔧 Resolving plan conflicts...');

      const plansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (!plansJson) return true;

      const plans: WeekPlan[] = JSON.parse(plansJson);
      const activePlans = plans.filter(plan => plan.isActive);

      if (activePlans.length <= 1) {
        console.log('✅ No conflicts found');
        return true;
      }

      console.log(`🔧 Found ${activePlans.length} active plans, resolving...`);

      const currentWeekInfo = this.getCurrentWeekInfo();

      // Prefer current week plan
      const currentWeekPlan = activePlans.find(plan =>
        plan.weekNumber === currentWeekInfo.weekNumber &&
        plan.year === currentWeekInfo.year
      );

      if (currentWeekPlan) {
        // Deactivate all others
        plans.forEach(plan => {
          plan.isActive = (plan === currentWeekPlan);
        });
        console.log('✅ Resolved conflict by selecting current week plan');
      } else {
        // Use most recent
        const mostRecent = activePlans.sort((a, b) => b.generatedAt - a.generatedAt)[0];
        plans.forEach(plan => {
          plan.isActive = (plan === mostRecent);
        });
        console.log('✅ Resolved conflict by selecting most recent plan');
      }

      await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(plans));
      await this.logRecoveryAction('CONFLICT_RESOLUTION', `Resolved ${activePlans.length} conflicting active plans`);

      return true;
    } catch (error) {
      console.error('❌ Plan conflict resolution failed:', error);
      await this.logRecoveryAction('CONFLICT_RESOLUTION_FAILED', `Resolution failed: ${error.message}`);
      return false;
    }
  }
}

export default WeeklyPlanManager.getInstance();
